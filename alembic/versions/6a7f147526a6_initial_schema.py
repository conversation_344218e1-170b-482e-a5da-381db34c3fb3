"""initial schema

Revision ID: 6a7f147526a6
Revises: 
Create Date: 2025-06-22 14:43:13.115633

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6a7f147526a6'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('franchisors',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('category', sa.String(length=100), nullable=False),
    sa.Column('sub_category', sa.String(length=100), nullable=True),
    sa.Column('region', sa.String(length=100), nullable=True),
    sa.Column('budget', sa.Float(), nullable=True),
    sa.Column('brochure_url', sa.String(length=500), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_franchisors_id'), 'franchisors', ['id'], unique=False)
    op.create_index(op.f('ix_franchisors_name'), 'franchisors', ['name'], unique=False)
    op.create_index(op.f('ix_franchisors_category'), 'franchisors', ['category'], unique=False)
    op.create_index(op.f('ix_franchisors_region'), 'franchisors', ['region'], unique=False)
    op.create_index(op.f('ix_franchisors_sub_category'), 'franchisors', ['sub_category'], unique=False)
    op.create_index(op.f('ix_franchisors_is_active'), 'franchisors', ['is_active'], unique=False)
    op.create_table('leads',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('zoho_lead_id', sa.String(length=100), nullable=True),
    sa.Column('full_name', sa.String(length=255), nullable=False),
    sa.Column('contact_number', sa.String(length=20), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('location', sa.String(length=255), nullable=True),
    sa.Column('lead_source', sa.String(length=100), nullable=True),
    sa.Column('franchise_preference', sa.String(length=255), nullable=True),
    sa.Column('budget_preference', sa.Numeric(precision=12, scale=2), nullable=True),
    sa.Column('qualification_status', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('zoho_lead_id')
    )
    op.create_index(op.f('ix_leads_contact_number'), 'leads', ['contact_number'], unique=False)
    op.create_table('questions',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('question_text', sa.Text(), nullable=False),
    sa.Column('question_type', sa.String(length=50), nullable=True),
    sa.Column('is_required', sa.Boolean(), nullable=True),
    sa.Column('order_sequence', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('system_settings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('setting_key', sa.String(length=100), nullable=False),
    sa.Column('setting_value', sa.Text(), nullable=True),
    sa.Column('setting_type', sa.String(length=50), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('setting_key')
    )
    op.create_table('users',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('mobile', sa.String(length=20), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=False),
    sa.Column('password_reset_token', sa.String(length=255), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('role', sa.Enum('ADMIN', 'USER', 'SUPERADMIN', name='user_role'), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_email_verified', sa.Boolean(), nullable=False),
    sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('remember_me_token', sa.String(length=255), nullable=True),
    sa.Column('remember_me_token_expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('mobile')
    )
    op.create_table('documents',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('franchise_id', sa.UUID(), nullable=True),
    sa.Column('filename', sa.String(length=255), nullable=False),
    sa.Column('file_path', sa.Text(), nullable=False),
    sa.Column('file_type', sa.String(length=50), nullable=True),
    sa.Column('processed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['franchise_id'], ['franchisors.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('lead_responses',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('lead_id', sa.UUID(), nullable=True),
    sa.Column('question_id', sa.UUID(), nullable=True),
    sa.Column('response_text', sa.Text(), nullable=True),
    sa.Column('answered_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lead_id'], ['leads.id'], ),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('refresh_tokens',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('token', sa.String(length=1000), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('refresh_tokens')
    op.drop_table('lead_responses')
    op.drop_table('documents')
    op.drop_table('users')
    op.drop_table('system_settings')
    op.drop_table('questions')
    op.drop_index(op.f('ix_leads_contact_number'), table_name='leads')
    op.drop_table('leads')
    op.drop_index(op.f('ix_franchisors_is_active'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_sub_category'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_region'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_category'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_name'), table_name='franchisors')
    op.drop_index(op.f('ix_franchisors_id'), table_name='franchisors')
    op.drop_table('franchisors')
    # ### end Alembic commands ###
