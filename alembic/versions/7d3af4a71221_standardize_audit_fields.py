"""standardize_audit_fields

Revision ID: 7d3af4a71221
Revises: 6a7f147526a6
Create Date: 2025-06-22 15:37:33.233311

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '7d3af4a71221'
down_revision: Union[str, None] = '6a7f147526a6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('ip_address', sa.String(), nullable=True),
    sa.Column('user_agent', sa.String(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_activity', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sessions_id'), 'sessions', ['id'], unique=False)
    op.add_column('documents', sa.Column('name', sa.String(length=255), nullable=False))
    op.add_column('documents', sa.Column('description', sa.Text(), nullable=True))
    op.add_column('documents', sa.Column('file_size', sa.String(length=20), nullable=True))
    op.add_column('documents', sa.Column('user_id', sa.UUID(), nullable=True))
    op.add_column('documents', sa.Column('franchisor_id', sa.UUID(), nullable=True))
    op.add_column('documents', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.alter_column('documents', 'file_path',
               existing_type=sa.TEXT(),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('documents', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.drop_constraint(op.f('documents_franchise_id_fkey'), 'documents', type_='foreignkey')
    op.create_foreign_key(None, 'documents', 'users', ['user_id'], ['id'])
    op.create_foreign_key(None, 'documents', 'franchisors', ['franchisor_id'], ['id'])
    op.drop_column('documents', 'processed')
    op.drop_column('documents', 'filename')
    op.drop_column('documents', 'franchise_id')
    op.add_column('lead_responses', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.add_column('lead_responses', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.alter_column('lead_responses', 'answered_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('leads', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('leads', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.add_column('questions', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False))
    op.alter_column('questions', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('system_settings', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    op.alter_column('system_settings', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('system_settings', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('system_settings', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('questions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.drop_column('questions', 'updated_at')
    op.alter_column('leads', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('leads', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('lead_responses', 'answered_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.drop_column('lead_responses', 'updated_at')
    op.drop_column('lead_responses', 'created_at')
    op.add_column('documents', sa.Column('franchise_id', sa.UUID(), autoincrement=False, nullable=True))
    op.add_column('documents', sa.Column('filename', sa.VARCHAR(length=255), autoincrement=False, nullable=False))
    op.add_column('documents', sa.Column('processed', sa.BOOLEAN(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'documents', type_='foreignkey')
    op.drop_constraint(None, 'documents', type_='foreignkey')
    op.create_foreign_key(op.f('documents_franchise_id_fkey'), 'documents', 'franchisors', ['franchise_id'], ['id'])
    op.alter_column('documents', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               nullable=True)
    op.alter_column('documents', 'file_path',
               existing_type=sa.String(length=500),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_column('documents', 'updated_at')
    op.drop_column('documents', 'franchisor_id')
    op.drop_column('documents', 'user_id')
    op.drop_column('documents', 'file_size')
    op.drop_column('documents', 'description')
    op.drop_column('documents', 'name')
    op.drop_index(op.f('ix_sessions_id'), table_name='sessions')
    op.drop_table('sessions')
    # ### end Alembic commands ###
