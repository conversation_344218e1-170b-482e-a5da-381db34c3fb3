"""
Test configuration and fixtures
"""
import os
import sys
import pytest
import pytest_asyncio
from typing import Async<PERSON>enerator, Generator
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import N<PERSON><PERSON><PERSON>
from datetime import datetime, timezone
from fastapi.testclient import TestClient
from app.core.config.settings import settings

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database.base import Base
from app.models.user import User
from app.core.security import password_hasher
from app.main import app
from app.core.database.connection import get_db
from app.models import base as models_base

# Create async test engine
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=settings.DB_ECHO,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW
)

# Create test session factory
TestingSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

@pytest.fixture(scope="session")
def event_loop() -> Generator:
    """Create an instance of the default event loop for each test case."""
    import asyncio
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_test_db():
    async with engine.begin() as conn:
        await conn.run_sync(models_base.Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(models_base.Base.metadata.drop_all)

@pytest_asyncio.fixture
async def test_session():
    """Create a test database session"""
    async with TestingSessionLocal() as session:
        yield session

@pytest.fixture
def test_client():
    """Create a test client"""
    return TestClient(app)

@pytest_asyncio.fixture
async def override_get_db(test_session):
    """Override get_db dependency"""
    async def _override_get_db():
        yield test_session
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()

@pytest_asyncio.fixture(scope="session")
async def test_db_async() -> AsyncGenerator:
    """Create test database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest_asyncio.fixture
async def db_session(test_db_async) -> AsyncGenerator[AsyncSession, None]:
    """Create a fresh database session for each test."""
    async with TestingSessionLocal() as session:
        yield session
        await session.rollback()

@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        password_hash=password_hasher.hash_password("testpassword"),
        is_active=True,
        is_email_verified=True,
        role="ADMIN",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user

@pytest_asyncio.fixture
async def client_async(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create an async client for testing."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def auth_headers(client_async: AsyncClient, test_user: User) -> dict:
    """Get authentication headers for test user."""
    response = await client_async.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "testpassword",
            "remember_me": False
        }
    )
    token = response.json()["data"]["access_token"]
    return {"Authorization": f"Bearer {token}"} 