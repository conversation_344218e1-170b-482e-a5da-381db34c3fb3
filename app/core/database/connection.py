"""
Database configuration for GrowthHive API
"""

from typing import AsyncGenerator
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.pool import NullPool
from app.core.config.settings import settings
from app.core.logging import logger

# Get database URL from settings
DATABASE_URL = settings.DATABASE_URL

# Create async engine with connection pooling disabled for development
try:
    async_engine = create_async_engine(
        DATABASE_URL,
        echo=settings.DEBUG,
        poolclass=NullPool
    )
    logger.info("✅ SQLAlchemy async engine created successfully")
except Exception as e:
    logger.error(f"⚠️ SQLAlchemy async engine creation failed: {e}")
    async_engine = None

# Session maker
AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
) if async_engine else None

# Base class for models
Base = declarative_base()

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()
