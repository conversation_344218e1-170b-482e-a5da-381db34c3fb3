"""Logging configuration for the GrowthHive API."""
import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Any, Dict, Optional
import json
from datetime import datetime
import time
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from app.core.config.settings import settings

def setup_logging() -> logging.Logger:
    """Setup logging configuration"""
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    logger = logging.getLogger("growthhive")
    logger.setLevel(settings.LOG_LEVEL)
    
    # Create formatters
    formatter = logging.Formatter(settings.LOG_FORMAT)
    
    # Create handlers
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    file_handler = RotatingFileHandler(
        log_dir / "app.log",
        maxBytes=10485760,  # 10MB
        backupCount=5
    )
    file_handler.setFormatter(formatter)
    
    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger

# Create logger instance
logger = setup_logging()

class JSONFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging"""
    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "environment": settings.ENVIRONMENT
        }
        
        if hasattr(record, "context"):
            log_data["context"] = record.context
            
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
            
        return json.dumps(log_data)

class PerformanceMiddleware(BaseHTTPMiddleware):
    """Middleware for monitoring request performance"""
    async def dispatch(self, request: Request, call_next: Response) -> Response:
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # Add process time to response headers
        response.headers["X-Process-Time"] = str(process_time)
        
        # Log if performance threshold is exceeded
        if process_time > settings.PERFORMANCE_THRESHOLD_MS / 1000:
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path}",
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "duration": process_time,
                    "threshold": settings.PERFORMANCE_THRESHOLD_MS,
                    "client_host": request.client.host if request.client else None
                }
            )
        
        return response

def log_error(error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
    """Log an error with optional context.
    
    Args:
        error: The exception to log
        context: Optional context dictionary
    """
    error_msg = f"Error: {str(error)}"
    if context:
        error_msg += f" | Context: {context}"
    logger.error(error_msg, exc_info=True, extra={"context": context})

def log_api_request(
    method: str,
    path: str,
    status_code: int,
    duration: float,
    client_host: Optional[str] = None,
    user_agent: Optional[str] = None
) -> None:
    """Log an API request with enhanced context.
    
    Args:
        method: HTTP method
        path: Request path
        status_code: Response status code
        duration: Request duration in seconds
        client_host: Client IP address
        user_agent: User agent string
    """
    logger.info(
        f"API Request: {method} {path} | Status: {status_code} | Duration: {duration:.2f}s",
        extra={
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration": duration,
            "client_host": client_host,
            "user_agent": user_agent,
            "environment": settings.ENVIRONMENT
        }
    )

def log_performance_metric(
    metric_name: str,
    value: float,
    threshold: Optional[float] = None,
    context: Optional[Dict[str, Any]] = None
) -> None:
    """Log a performance metric.
    
    Args:
        metric_name: Name of the metric
        value: Metric value
        threshold: Optional threshold value
        context: Optional context dictionary
    """
    log_data = {
        "metric": metric_name,
        "value": value,
        "threshold": threshold,
        "context": context or {}
    }
    
    if threshold and value > threshold:
        logger.warning(
            f"Performance threshold exceeded: {metric_name}",
            extra=log_data
        )
    else:
        logger.info(
            f"Performance metric: {metric_name}",
            extra=log_data
        ) 