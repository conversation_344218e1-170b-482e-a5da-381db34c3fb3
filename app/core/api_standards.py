"""
API Development Standards and Guidelines for GrowthHive
This module defines consistent patterns for API development across the project.
"""

from typing import Any, Dict, List, Optional
from fastapi import Request
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

from app.core.logging import setup_logging
from app.schemas.base_response import StandardResponse as ResponseModel, MessageResponse as MessageModel
from app.core.responses.models import DataModel

# Setup logging
logger = setup_logging()

# Default response headers
DEFAULT_RESPONSE_HEADERS = {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
    "Content-Security-Policy": "default-src 'self'",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
}

# Default error response
DEFAULT_ERROR_RESPONSE = {
    "status": "error",
    "message": "An error occurred",
    "data": None
}

# Default success response
DEFAULT_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Operation successful",
    "data": None
}

# Default pagination response
DEFAULT_PAGINATION_RESPONSE = {
    "status": "success",
    "message": "Data retrieved successfully",
    "data": {
        "items": [],
        "total": 0,
        "page": 1,
        "size": 10,
        "pages": 1
    }
}

# Error codes
class ErrorCodes:
    """Error codes for the application"""
    VALIDATION_ERROR = 4000
    AUTHENTICATION_ERROR = 4001
    AUTHORIZATION_ERROR = 4002
    NOT_FOUND_ERROR = 4003
    INTERNAL_SERVER_ERROR = 5000

# Error messages
class ErrorMessages:
    """Error messages for the application"""
    VALIDATION_ERROR = "Validation error"
    AUTHENTICATION_ERROR = "Authentication error"
    AUTHORIZATION_ERROR = "Authorization error"
    NOT_FOUND_ERROR = "Resource not found"
    INTERNAL_SERVER_ERROR = "Internal server error"


class APIStandards:
    """Central class for API development standards and utilities"""
    
    # Standard HTTP Status Codes
    HTTP_200_OK = 200
    HTTP_201_CREATED = 201
    HTTP_204_NO_CONTENT = 204
    HTTP_400_BAD_REQUEST = 400
    HTTP_401_UNAUTHORIZED = 401
    HTTP_403_FORBIDDEN = 403
    HTTP_404_NOT_FOUND = 404
    HTTP_409_CONFLICT = 409
    HTTP_422_UNPROCESSABLE_ENTITY = 422
    HTTP_500_INTERNAL_SERVER_ERROR = 500
    
    @staticmethod
    def create_success_response(
        data: Any = None,
        message: str = "Operation completed successfully",
        title: str = "Success",
        status_code: int = 200
    ) -> JSONResponse:
        """
        Create standardized success response
        
        Args:
            data: Response data
            message: Success message description
            title: Success message title
            status_code: HTTP status code
            
        Returns:
            JSONResponse: Standardized success response
        """
        # Check if data already has a 'details' field (like login response)
        if isinstance(data, dict) and 'details' in data:
            # Use the data directly without wrapping in DataModel
            response_data = ResponseModel(
                success=True,
                message=MessageModel(title=title, description=message),
                data=data,
                error_code=0
            )
        else:
            # Wrap in DataModel for standard responses
            response_data = ResponseModel(
                success=True,
                message=MessageModel(title=title, description=message),
                data=DataModel(details=data) if data is not None else DataModel(),
                error_code=0
            )
        
        return JSONResponse(
            content=response_data.model_dump(),
            status_code=status_code
        )
    
    @staticmethod
    def create_error_response(
        error_message: str,
        error_title: str = "Error",
        status_code: int = 400,
        error_code: int = ErrorCodes.VALIDATION_ERROR,
        details: Any = None
    ) -> JSONResponse:
        """
        Create standardized error response
        
        Args:
            error_message: Error description
            error_title: Error title
            status_code: HTTP status code
            error_code: Custom error code
            details: Additional error details
            
        Returns:
            JSONResponse: Standardized error response
        """
        if details:
            data_model = DataModel(details=details)
        else:
            data_model = None

        response_data = ResponseModel(
            success=False,
            message=MessageModel(title=error_title, description=error_message),
            data=data_model,
            error_code=error_code
        )
        
        return JSONResponse(
            content=response_data.model_dump(),
            status_code=status_code
        )
    
    @staticmethod
    def log_api_request(request: Request, user_id: str = None):
        """
        Log API request with structured format
        
        Args:
            request: FastAPI request object
            user_id: Optional user ID for tracking
        """
        logger.info(
            "API Request",
            extra={
                "method": request.method,
                "url": str(request.url),
                "user_id": user_id,
                "user_agent": request.headers.get("user-agent"),
                "ip_address": request.client.host if request.client else None,
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    
    @staticmethod
    def log_api_response(request: Request, status_code: int, response_time: float, user_id: str = None):
        """
        Log API response with structured format
        
        Args:
            request: FastAPI request object
            status_code: HTTP status code
            response_time: Response time in seconds
            user_id: Optional user ID for tracking
        """
        logger.info(
            "API Response",
            extra={
                "method": request.method,
                "url": str(request.url),
                "status_code": status_code,
                "response_time": response_time,
                "user_id": user_id,
                "timestamp": datetime.utcnow().isoformat()
            }
        )


class PaginationParams(BaseModel):
    """Standard pagination parameters"""
    skip: int = Field(default=0, ge=0, description="Number of records to skip")
    limit: int = Field(default=100, ge=1, le=1000, description="Maximum number of records to return")


class PaginationResponse(BaseModel):
    """Standard pagination response metadata"""
    total: int = Field(description="Total number of records")
    skip: int = Field(description="Number of records skipped")
    limit: int = Field(description="Maximum number of records returned")
    has_more: bool = Field(description="Whether there are more records available")


class StandardListResponse(BaseModel):
    """Standard response format for list endpoints"""
    items: List[Any] = Field(description="List of items")
    pagination: PaginationResponse = Field(description="Pagination metadata")


# Common validation patterns
class EmailValidation:
    """Email validation patterns"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    message = "Please provide a valid email address"


class PhoneValidation:
    """Australian phone number validation patterns"""
    mobile_pattern = r'^\+?61[4-5]\d{8}$|^0[4-5]\d{8}$'
    landline_pattern = r'^\+?61[2-8]\d{8}$|^0[2-8]\d{8}$'
    message = "Please provide a valid Australian phone number"


class PasswordValidation:
    """Password validation requirements"""
    min_length = 8
    pattern = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{}|;\':",./<>?]).{8,}$'
    message = "Password must be at least 8 characters long and contain uppercase, lowercase, digit, and special character"


# API Documentation Standards
class APIDocumentation:
    """API documentation standards and utilities"""
    
    @staticmethod
    def get_standard_responses() -> Dict[int, Dict]:
        """Get standard API responses for documentation"""
        return {
            200: {
                "description": "Successful operation",
                "content": {
                    "application/json": {
                        "example": {
                            "success": True,
                            "message": "Operation successful",
                            "data": None
                        }
                    }
                }
            },
            400: {
                "description": "Bad request",
                "content": {
                    "application/json": {
                        "example": {
                            "success": False,
                            "message": "Validation error",
                            "data": None,
                            "error_code": 4000
                        }
                    }
                }
            },
            401: {
                "description": "Unauthorized",
                "content": {
                    "application/json": {
                        "example": {
                            "success": False,
                            "message": "Authentication error",
                            "data": None,
                            "error_code": 4001
                        }
                    }
                }
            },
            403: {
                "description": "Forbidden",
                "content": {
                    "application/json": {
                        "example": {
                            "success": False,
                            "message": "Authorization error",
                            "data": None,
                            "error_code": 4002
                        }
                    }
                }
            },
            404: {
                "description": "Not found",
                "content": {
                    "application/json": {
                        "example": {
                            "success": False,
                            "message": "Resource not found",
                            "data": None,
                            "error_code": 4003
                        }
                    }
                }
            },
            500: {
                "description": "Internal server error",
                "content": {
                    "application/json": {
                        "example": {
                            "success": False,
                            "message": "Internal server error",
                            "data": None,
                            "error_code": 5000
                        }
                    }
                }
            }
        }


class SecurityStandards:
    """Security-related standards and utilities"""
    
    # Security headers
    SECURITY_HEADERS = {
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
        "Content-Security-Policy": "default-src 'self'",
        "Referrer-Policy": "strict-origin-when-cross-origin",
        "Permissions-Policy": "geolocation=(), microphone=(), camera=()"
    }
    
    @staticmethod
    def sanitize_input(data: str) -> str:
        """
        Sanitize user input to prevent XSS attacks
        
        Args:
            data: Input string to sanitize
            
        Returns:
            Sanitized string
        """
        # Basic XSS prevention
        data = data.replace("<", "&lt;")
        data = data.replace(">", "&gt;")
        data = data.replace('"', "&quot;")
        data = data.replace("'", "&#x27;")
        data = data.replace("&", "&amp;")
        return data


class ErrorCode(str, Enum):
    """Standard error codes for the API"""
    BAD_REQUEST = "BAD_REQUEST"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    CONFLICT = "CONFLICT"


class ErrorResponse(BaseModel):
    """Standard error response model"""
    code: ErrorCode = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    details: Optional[str] = Field(None, description="Detailed error information")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")


class APIResponse(BaseModel):
    """Standard API response model"""
    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Response timestamp")
