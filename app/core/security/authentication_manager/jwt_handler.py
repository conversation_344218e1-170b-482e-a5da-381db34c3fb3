"""JWT token handler for authentication"""
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any
from jose import jwt

from app.core.config.settings import settings
from app.core.utils.exception_manager.custom_exceptions import AuthenticationError
from app.schemas.base_response import ResponseMessage
from app.core.logging import logger


class J<PERSON><PERSON><PERSON><PERSON>:
    """JWT token handler for authentication"""
    
    def __init__(self) -> None:
        """Initialize JWT handler with settings"""
        self.secret_key = settings.JWT_SECRET_KEY
        self.algorithm = settings.JWT_ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
        
    def create_token(
        self,
        data: Dict[str, Any],
        token_type: str = "access",
        remember_me: bool = False,
        short_lived: bool = False
    ) -> str:
        """Create JWT token with enhanced expiration logic and validation

        Args:
            data: Data to encode in token (validated)
            token_type: Type of token (access or refresh)
            remember_me: Whether this is for a remember me session
            short_lived: Whether to create a short-lived token (for remember me fallback)

        Returns:
            str: JWT token

        Raises:
            AuthenticationError: If token creation fails
        """
        # Input validation
        if not data or not isinstance(data, dict):
            raise AuthenticationError(
                "invalid_token_data",
                message=ResponseMessage(
                    title="Token Creation Failed",
                    description="Invalid token data provided."
                )
            )

        # Validate required fields
        required_fields = ["user_id", "email"]
        for field in required_fields:
            if field not in data or not data[field]:
                raise AuthenticationError(
                    "missing_token_field",
                    message=ResponseMessage(
                        title="Token Creation Failed",
                        description=f"Required field '{field}' missing from token data."
                    )
                )

        # Validate token type
        valid_types = ["access", "refresh"]
        if token_type not in valid_types:
            raise AuthenticationError(
                "invalid_token_type",
                message=ResponseMessage(
                    title="Token Creation Failed",
                    description=f"Invalid token type '{token_type}'. Must be one of: {valid_types}"
                )
            )

        try:
            # Create token data with validation
            to_encode = self._prepare_token_data(data.copy())

            # Set expiration time with enhanced logic and validation
            expire = self._calculate_expiration(token_type, remember_me, short_lived)

            # Add metadata
            to_encode.update({
                "exp": expire,
                "iat": datetime.utcnow(),  # Issued at
                "type": token_type,
                "remember_me": remember_me,
                "short_lived": short_lived,
                "version": "1.0"  # Token version for future compatibility
            })

            # Create token with error handling
            token = jwt.encode(
                to_encode,
                self.secret_key,
                algorithm=self.algorithm
            )

            # Validate created token
            if not token or len(token) < 50:  # Reasonable minimum length
                raise AuthenticationError(
                    "token_creation_failed",
                    message=ResponseMessage(
                        title="Token Creation Failed",
                        description="Generated token is invalid."
                    )
                )

            logger.info(
                f"Successfully created {token_type} token",
                extra={
                    "token_type": token_type,
                    "expires_at": expire.isoformat(),
                    "remember_me": remember_me,
                    "short_lived": short_lived,
                    "user_id": data.get("user_id", "unknown")
                }
            )

            return token

        except jwt.JWTError as e:
            logger.error(f"JWT encoding error: {e}")
            raise AuthenticationError(
                "jwt_encoding_error",
                message=ResponseMessage(
                    title="Token Creation Failed",
                    description="Failed to encode JWT token."
                ),
                details={"error": str(e)}
            )
        except Exception as e:
            logger.error(f"Unexpected error creating token: {e}")
            raise AuthenticationError(
                "token_creation_error",
                message=ResponseMessage(
                    title="Token Creation Failed",
                    description="An unexpected error occurred during token creation."
                ),
                details={"error": str(e)}
            )

    def _prepare_token_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare and validate token data"""
        # Sanitize user_id and email
        if "user_id" in data:
            data["user_id"] = str(data["user_id"]).strip()
        if "email" in data:
            data["email"] = str(data["email"]).strip().lower()

        # Add subject (standard JWT claim)
        data["sub"] = data["email"]

        return data

    def _calculate_expiration(self, token_type: str, remember_me: bool, short_lived: bool) -> datetime:
        """Calculate token expiration with validation"""
        now = datetime.utcnow()

        if token_type == "access":
            if short_lived:
                # Short-lived tokens for remember me fallback
                minutes = getattr(settings, 'JWT_SHORT_LIVED_TOKEN_MINUTES', 15)
                expire = now + timedelta(minutes=max(5, min(60, minutes)))  # 5-60 min range
            elif remember_me:
                # Extended tokens when remember me is enabled
                days = getattr(settings, 'JWT_REMEMBER_ME_TOKEN_EXPIRE_DAYS', 30)
                expire = now + timedelta(days=max(1, min(90, days)))  # 1-90 day range
            else:
                # Standard tokens - always use ACCESS_TOKEN_EXPIRE_MINUTES
                minutes = getattr(settings, 'ACCESS_TOKEN_EXPIRE_MINUTES', 15)
                expire = now + timedelta(minutes=minutes)  # Use exact minutes from settings
        else:  # refresh token
            if remember_me:
                # Long-lived refresh tokens for remember me
                days = getattr(settings, 'JWT_REMEMBER_ME_REFRESH_TOKEN_EXPIRE_DAYS', 90)
                expire = now + timedelta(days=max(30, min(365, days)))  # 30-365 day range
            else:
                # Standard refresh tokens
                days = getattr(settings, 'REFRESH_TOKEN_EXPIRE_DAYS', 7)
                expire = now + timedelta(days=days)  # Use exact days from settings

        return expire
            
    async def decode_token(self, token: str) -> Dict[str, Any]:
        """Decode and verify JWT token with comprehensive validation

        Args:
            token: JWT token to decode (will be validated)

        Returns:
            Dict[str, Any]: Decoded and validated token data

        Raises:
            AuthenticationError: If token is invalid, expired, or fails validation
        """
        # Input validation
        if not token or not isinstance(token, str):
            raise AuthenticationError(
                "invalid_token_input",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Token must be a non-empty string."
                )
            )

        # Sanitize token
        token = token.strip()

        # Basic format validation
        if len(token) < 50 or len(token) > 2000:  # Reasonable JWT length bounds
            raise AuthenticationError(
                "invalid_token_length",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Token length is invalid."
                )
            )

        # Check JWT structure (should have 3 parts separated by dots)
        token_parts = token.split('.')
        if len(token_parts) != 3:
            raise AuthenticationError(
                "malformed_token",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Token structure is malformed."
                )
            )

        try:
            # Decode token with comprehensive options
            payload = jwt.decode(
                token,
                self.secret_key,
                algorithms=[self.algorithm],
                options={
                    "verify_signature": True,
                    "verify_exp": True,
                    "verify_iat": True,
                    "require_exp": True,
                    "require_iat": False
                }
            )

            # Additional payload validation
            validated_payload = self._validate_token_payload(payload)

            logger.info(
                "Successfully decoded and validated token",
                extra={
                    "token_type": validated_payload.get("type"),
                    "user_id": validated_payload.get("user_id"),
                    "expires_at": datetime.fromtimestamp(validated_payload.get("exp")).isoformat()
                }
            )

            return validated_payload

        except jwt.ExpiredSignatureError:
            logger.warning("Token has expired")
            raise AuthenticationError(
                "token_expired",
                message=ResponseMessage(
                    title="Token Expired",
                    description="Your session has expired. Please login again."
                )
            )
        except jwt.JWTError as e:
            logger.error(f"JWT decoding error: {e}")
            raise AuthenticationError(
                "jwt_decoding_error",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Failed to decode JWT token."
                ),
                details={"error": str(e)}
            )
        except Exception as e:
            logger.error(f"Unexpected error decoding token: {e}")
            raise AuthenticationError(
                "token_decoding_error",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="An unexpected error occurred during token validation."
                ),
                details={"error": str(e)}
            )

    def _validate_token_payload(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Validate token payload structure and content
        
        Args:
            payload: Decoded token payload
            
        Returns:
            Dict[str, Any]: Validated payload
            
        Raises:
            AuthenticationError: If payload validation fails
        """
        # Check required fields
        required_fields = ["user_id", "email", "exp", "type"]
        for field in required_fields:
            if field not in payload:
                raise AuthenticationError(
                    "missing_token_field",
                    message=ResponseMessage(
                        title="Invalid Token",
                        description=f"Required field '{field}' missing from token."
                    )
                )

        # Validate token type
        if payload["type"] not in ["access", "refresh"]:
            raise AuthenticationError(
                "invalid_token_type",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Invalid token type."
                )
            )

        # Validate expiration
        try:
            exp = payload["exp"]
            if not isinstance(exp, (int, float)) or exp <= 0:
                raise AuthenticationError(
                    "invalid_expiration",
                    message=ResponseMessage(
                        title="Invalid Token",
                        description="Invalid token expiration."
                    )
                )
        except (KeyError, TypeError):
            raise AuthenticationError(
                "missing_expiration",
                message=ResponseMessage(
                    title="Invalid Token",
                    description="Token expiration is missing."
                )
            )

        return payload

    async def verify_token(self, token: str) -> bool:
        """Verify JWT token without raising exceptions
        
        Args:
            token: JWT token to verify
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            await self.decode_token(token)
            return True
        except AuthenticationError:
            return False 