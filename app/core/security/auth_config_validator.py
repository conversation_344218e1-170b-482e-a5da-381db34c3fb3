"""
Authentication Configuration Validator
Ensures all authentication settings are properly configured and secure
"""

from typing import List, Dict, Any
from app.core.config.settings import settings
from app.core.logging import logger


class AuthConfigValidator:
    """Validates authentication configuration for security and completeness"""
    
    def __init__(self) -> None:
        """Initialize validator with settings"""
        self.settings = settings
        self.validation_errors: List[str] = []
        self.validation_warnings: List[str] = []
    
    def validate_all(self) -> Dict[str, Any]:
        """
        Validate all authentication configuration
        
        Returns:
            Dict containing validation results
        """
        self.validation_errors.clear()
        self.validation_warnings.clear()
        
        # Validate JWT configuration
        self._validate_jwt_config()
        
        # Validate remember me configuration
        self._validate_remember_me_config()
        
        # Validate security settings
        self._validate_security_settings()
        
        # Validate database settings
        self._validate_database_settings()
        
        return {
            "valid": len(self.validation_errors) == 0,
            "errors": self.validation_errors,
            "warnings": self.validation_warnings,
            "total_issues": len(self.validation_errors) + len(self.validation_warnings)
        }
    
    def _validate_jwt_config(self) -> None:
        """Validate JWT configuration"""
        # JWT Secret Key
        if not hasattr(self.settings, 'JWT_SECRET_KEY') or not self.settings.JWT_SECRET_KEY:
            raise ValueError("JWT_SECRET_KEY is required")
        elif len(self.settings.JWT_SECRET_KEY) < 32:
            raise ValueError("JWT_SECRET_KEY must be at least 32 characters long")
        elif self.settings.JWT_SECRET_KEY == "your-super-secret-jwt-key-change-in-production-32-chars":
            self.validation_warnings.append("JWT_SECRET_KEY is using default value - change in production")
        
        # JWT Algorithm
        if not hasattr(self.settings, 'ALGORITHM') or not self.settings.ALGORITHM:
            self.validation_errors.append("ALGORITHM is not configured")
        elif self.settings.ALGORITHM not in ['HS256', 'HS384', 'HS512']:
            self.validation_warnings.append(f"ALGORITHM '{self.settings.ALGORITHM}' may not be secure")
        
        # Token expiration times
        if not hasattr(self.settings, 'ACCESS_TOKEN_EXPIRE_MINUTES'):
            self.validation_errors.append("ACCESS_TOKEN_EXPIRE_MINUTES is not configured")
        elif self.settings.ACCESS_TOKEN_EXPIRE_MINUTES < 5:
            self.validation_warnings.append("ACCESS_TOKEN_EXPIRE_MINUTES is very short (< 5 minutes)")
        elif self.settings.ACCESS_TOKEN_EXPIRE_MINUTES > 480:  # 8 hours
            self.validation_warnings.append("ACCESS_TOKEN_EXPIRE_MINUTES is very long (> 8 hours)")
        
        if not hasattr(self.settings, 'REFRESH_TOKEN_EXPIRE_DAYS'):
            self.validation_errors.append("REFRESH_TOKEN_EXPIRE_DAYS is not configured")
        elif self.settings.REFRESH_TOKEN_EXPIRE_DAYS > 90:
            self.validation_warnings.append("REFRESH_TOKEN_EXPIRE_DAYS is very long (> 90 days)")
    
    def _validate_remember_me_config(self) -> None:
        """Validate remember me configuration"""
        if not hasattr(self.settings, 'REMEMBER_ME_TOKEN_LENGTH'):
            self.validation_errors.append("REMEMBER_ME_TOKEN_LENGTH is not configured")
        elif self.settings.REMEMBER_ME_TOKEN_LENGTH < 16:
            self.validation_warnings.append("REMEMBER_ME_TOKEN_LENGTH should be at least 16 for security")
        elif self.settings.REMEMBER_ME_TOKEN_LENGTH > 64:
            self.validation_warnings.append("REMEMBER_ME_TOKEN_LENGTH is unnecessarily large (> 64)")
    
    def _validate_security_settings(self) -> None:
        """Validate general security settings"""
        # CORS settings
        if hasattr(self.settings, 'CORS_ORIGINS'):
            if "*" in self.settings.CORS_ORIGINS:
                self.validation_warnings.append("CORS_ORIGINS allows all origins (*) - not recommended for production")
        
        # Allowed hosts
        if hasattr(self.settings, 'ALLOWED_HOSTS'):
            if "*" in self.settings.ALLOWED_HOSTS:
                self.validation_warnings.append("ALLOWED_HOSTS allows all hosts (*) - not recommended for production")
    
    def _validate_database_settings(self) -> None:
        """Validate database configuration"""
        # Check if database URL is configured
        if not hasattr(self.settings, 'DATABASE_URL') or not self.settings.DATABASE_URL:
            # Check individual database settings
            if not hasattr(self.settings, 'DATABASE_HOST') or not self.settings.DATABASE_HOST:
                self.validation_errors.append("DATABASE_HOST is not configured")
            if not hasattr(self.settings, 'DATABASE_PORT') or not self.settings.DATABASE_PORT:
                self.validation_errors.append("DATABASE_PORT is not configured")
            if not hasattr(self.settings, 'DATABASE_USER') or not self.settings.DATABASE_USER:
                self.validation_errors.append("DATABASE_USER is not configured")
            if not hasattr(self.settings, 'DATABASE_PASSWORD') or not self.settings.DATABASE_PASSWORD:
                self.validation_errors.append("DATABASE_PASSWORD is not configured")
            if not hasattr(self.settings, 'DATABASE_NAME') or not self.settings.DATABASE_NAME:
                self.validation_errors.append("DATABASE_NAME is not configured")
    
    def log_validation_results(self) -> Dict[str, Any]:
        """Log validation results
        
        Returns:
            Dict containing validation results
        """
        results = self.validate_all()
        
        if results["valid"]:
            logger.info("✅ Authentication configuration validation passed")
        else:
            logger.error(f"❌ Authentication configuration validation failed with {len(results['errors'])} errors")
        
        for error in results["errors"]:
            logger.error(f"CONFIG ERROR: {error}")
        
        for warning in results["warnings"]:
            logger.warning(f"CONFIG WARNING: {warning}")
        
        return results


# Global validator instance
auth_config_validator = AuthConfigValidator()


def validate_auth_config_on_startup() -> Dict[str, Any]:
    """Validate authentication configuration on application startup
    
    Returns:
        Dict containing validation results
    """
    logger.info("Validating authentication configuration...")
    results = auth_config_validator.log_validation_results()
    
    if not results["valid"]:
        logger.error("Authentication configuration has critical errors!")
        # In production, you might want to raise an exception here
        # raise RuntimeError("Authentication configuration validation failed")
    
    return results
