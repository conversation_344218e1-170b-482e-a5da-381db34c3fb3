"""
Enhanced authentication middleware with precise error handling
"""

from typing import Dict, Any, Optional
from fastapi import Depends, Request, HTTPException
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError

from app.core.database.management import get_db
from app.models.user import User
from app.core.security.authentication_manager.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.security.auth_error_handler import Auth<PERSON><PERSON>r<PERSON>and<PERSON>, AuthErrorType
from app.core.security.remember_token_manager import remember_token_manager
from app.core.utils.exception_manager.custom_exceptions import AuthenticationError
import logging

logger = logging.getLogger(__name__)


class EnhancedAuthMiddleware:
    """Enhanced authentication middleware with precise error handling"""
    
    def __init__(self):
        """Initialize the enhanced authentication middleware"""
        self.jwt_handler = <PERSON><PERSON><PERSON><PERSON><PERSON>()
        self.auth_error_handler = AuthErrorHandler()
        self.security = HTTPBearer(auto_error=False)  # Don't auto-error, handle manually
    
    async def get_current_user(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
        db: AsyncSession = Depends(get_db)
    ) -> Dict[str, Any]:
        """
        Get current authenticated user with enhanced error handling and remember_me_token fallback

        Args:
            request: FastAPI request object
            credentials: HTTP authorization credentials
            db: Database session

        Returns:
            Dict[str, Any]: User data from token (includes new_jwt if remember token was used)

        Raises:
            HTTPException: Authentication error with 401 status code
        """
        try:
            # First, try JWT authentication
            jwt_result = await self._try_jwt_authentication(request, credentials, db)
            if jwt_result:
                return jwt_result

            # If JWT fails, try remember_me_token fallback
            remember_result = await self._try_remember_token_authentication(request, db)
            if remember_result:
                # If remember token was used and new JWT was generated, store it in request state
                if "new_jwt" in remember_result:
                    request.state.new_jwt_token = remember_result["new_jwt"]
                return remember_result

            # If both fail, raise authentication error
            logger.warning(f"Authentication failed for {request.url.path}")
            raise HTTPException(
                status_code=401,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"}
            )

        except HTTPException:
            # Re-raise HTTPException as-is
            raise
        except AuthenticationError as e:
            # Convert AuthenticationError to HTTPException
            raise HTTPException(
                status_code=401,
                detail=str(e),
                headers={"WWW-Authenticate": "Bearer"}
            )
        except Exception as e:
            # Unexpected errors
            logger.error(f"Unexpected error in authentication middleware: {e}")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed",
                headers={"WWW-Authenticate": "Bearer"}
            )

    async def _try_jwt_authentication(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials],
        db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        Try JWT authentication with comprehensive validation

        Returns:
            Optional[Dict[str, Any]]: User data if successful, None if failed
        """
        try:
            # Validate request object
            if not request:
                logger.error("Invalid request object in JWT authentication")
                return None

            # Check if authorization header exists
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                return None

            # Validate authorization header format
            header_error = self.auth_error_handler.handle_authorization_header_error(auth_header)
            if header_error:
                return None

            # Extract and validate credentials
            if not credentials or not credentials.credentials:
                return None

            token = credentials.credentials.strip()

            # Basic token validation
            if not token or len(token) < 50:
                return None

            # Decode and validate token with enhanced error handling
            try:
                payload = await self.jwt_handler.decode_token(token)
            except AuthenticationError as e:
                # Log specific authentication errors for monitoring
                logger.debug(f"JWT authentication failed: {e}")
                return None
            except Exception as e:
                logger.error(f"Unexpected error during JWT decoding: {e}")
                return None

            # Verify token type (already validated in JWT handler, but double-check)
            if payload.get("type") != "access":
                logger.warning(f"Invalid token type in JWT authentication: {payload.get('type')}")
                return None

            # Extract and validate user data from token
            user_id = payload.get("user_id")
            email = payload.get("email")

            if not user_id or not email:
                logger.warning("Missing user_id or email in JWT payload")
                return None

            # Database validation with enhanced error handling
            try:
                # Validate database session
                if not db:
                    logger.error("Invalid database session in JWT authentication")
                    return None

                # Query user with comprehensive checks
                user_query = await db.execute(
                    select(User).where(
                        User.id == user_id,
                        User.email == email,
                        User.is_active
                    )
                )
                user = user_query.scalar_one_or_none()

                if not user:
                    logger.warning(f"User not found or inactive for JWT: user_id={user_id}, email={email}")
                    return None

                # Additional security checks
                if not self._validate_user_security(user):
                    logger.warning(f"User failed security validation: {user.email}")
                    return None

                # Log successful JWT authentication
                logger.info(f"Successful JWT authentication for user: {email}")

                return {
                    "user_id": user_id,
                    "email": email,
                    "role": user.role,
                    "is_active": user.is_active,
                    "user": user,
                    "auth_method": "jwt",
                    "token_type": payload.get("type"),
                    "remember_me": payload.get("remember_me", False)
                }

            except SQLAlchemyError as e:
                logger.error(f"Database error during JWT user verification: {e}")
                return None
            except Exception as e:
                logger.error(f"Unexpected error during user verification: {e}")
                return None

        except Exception as e:
            logger.error(f"Unexpected error in JWT authentication: {e}")
            return None

    def _validate_user_security(self, user: User) -> bool:
        """
        Additional security validation for user

        Args:
            user: User object to validate

        Returns:
            bool: True if user passes security checks
        """
        try:
            # Check if user object is valid
            if not user or not hasattr(user, 'id') or not hasattr(user, 'email'):
                return False

            # Check if user is active
            if not user.is_active:
                return False

            # Check if email is valid
            if not user.email or "@" not in user.email:
                return False

            # Additional checks can be added here
            # - Account lockout status
            # - Password expiry
            # - Role validation
            # - etc.

            return True

        except Exception as e:
            logger.error(f"Error in user security validation: {e}")
            return False

    async def _try_remember_token_authentication(
        self,
        request: Request,
        db: AsyncSession
    ) -> Optional[Dict[str, Any]]:
        """
        Try remember_me_token authentication with comprehensive security checks

        Returns:
            Optional[Dict[str, Any]]: User data with new JWT if successful, None if failed
        """
        try:
            # Validate inputs
            if not request or not db:
                logger.error("Invalid request or database session in remember token authentication")
                return None

            # Extract remember token from request with validation
            remember_token = remember_token_manager.extract_remember_token_from_request(request)

            if not remember_token:
                return None

            # Validate token format with enhanced checks
            if not remember_token_manager.validate_remember_token_format(remember_token):
                logger.warning(f"Invalid remember token format: {remember_token[:10]}...")
                # Consider rate limiting here for security
                return None

            # Authenticate with remember token (includes comprehensive validation)
            user = await remember_token_manager.authenticate_with_remember_token(remember_token, db)

            if not user:
                return None

            # Additional security validation
            if not self._validate_user_security(user):
                logger.warning(f"User failed security validation in remember token auth: {user.email}")
                return None

            # Prepare token data with validation
            token_data = self._prepare_token_data_for_user(user)
            if not token_data:
                logger.error(f"Failed to prepare token data for user: {user.email}")
                return None

            # Generate new short-lived JWT for this session with error handling
            try:
                new_jwt = await self.jwt_handler.create_token(
                    data=token_data,
                    token_type="access",
                    remember_me=True,
                    short_lived=True  # 15-minute token
                )
            except AuthenticationError as e:
                logger.error(f"Failed to create JWT for remember token user {user.email}: {e}")
                return None
            except Exception as e:
                logger.error(f"Unexpected error creating JWT for remember token user {user.email}: {e}")
                return None

            # Validate generated JWT
            if not new_jwt or len(new_jwt) < 50:
                logger.error(f"Generated JWT is invalid for user: {user.email}")
                return None

            logger.info(f"Successful remember token authentication for user: {user.email}")

            return {
                "user_id": str(user.id),
                "email": user.email,
                "role": user.role,
                "is_active": user.is_active,
                "user": user,
                "auth_method": "remember_token",
                "new_jwt": new_jwt,  # Include new JWT for client to update
                "token_refreshed": True
            }

        except SQLAlchemyError as e:
            logger.error(f"Database error in remember token authentication: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in remember token authentication: {e}")
            return None

    def _prepare_token_data_for_user(self, user: User) -> Optional[Dict[str, Any]]:
        """
        Prepare token data for user with validation

        Args:
            user: User object

        Returns:
            Optional[Dict[str, Any]]: Token data or None if validation fails
        """
        try:
            if not user or not user.id or not user.email:
                return None

            # Validate email format
            if "@" not in user.email:
                logger.error(f"Invalid email format for user: {user.id}")
                return None

            return {
                "user_id": str(user.id),
                "email": user.email.strip().lower()
            }

        except Exception as e:
            logger.error(f"Error preparing token data for user: {e}")
            return None

    
    async def get_current_active_user(
        self,
        current_user: Dict[str, Any] = Depends(lambda: enhanced_auth_middleware.get_current_user)
    ) -> Dict[str, Any]:
        """
        Get current active user (additional check for active status)
        
        Args:
            current_user: Current user from get_current_user dependency
            
        Returns:
            Dict[str, Any]: Current active user data
            
        Raises:
            JSONResponse: Error response if user is inactive
        """
        # If we get here, the user is already verified as active by get_current_user
        # This is just for compatibility with existing code
        if not current_user.get("is_active", True):
            return self.auth_error_handler.create_auth_error(AuthErrorType.ACCOUNT_DISABLED)
        
        return current_user
    
    async def verify_admin_user(
        self,
        current_user: Dict[str, Any] = Depends(lambda: enhanced_auth_middleware.get_current_user)
    ) -> Dict[str, Any]:
        """
        Verify that current user has admin role
        
        Args:
            current_user: Current user from get_current_user dependency
            
        Returns:
            Dict[str, Any]: Current user data if admin
            
        Raises:
            JSONResponse: Error response if user is not admin
        """
        if current_user.get("role") != "admin":
            logger.warning(f"Non-admin user attempted admin access: {current_user.get('email')}")
            return self.auth_error_handler.create_auth_error(
                AuthErrorType.INSUFFICIENT_PERMISSIONS,
                custom_message="Admin privileges required to access this resource."
            )
        
        return current_user
    
    async def verify_token_only(
        self,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> bool:
        """
        Verify token without database lookup (for performance-critical endpoints)
        
        Args:
            credentials: HTTP authorization credentials
            
        Returns:
            bool: True if token is valid, False otherwise
        """
        try:
            if not credentials:
                return False
            
            token = credentials.credentials
            if not token:
                return False
            
            # Just verify token signature and expiration
            payload = await self.jwt_handler.decode_token(token)
            return payload.get("type") == "access"
            
        except Exception:
            return False


# Global instance
enhanced_auth_middleware = EnhancedAuthMiddleware()

# Dependencies for use in endpoints
async def get_current_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: AsyncSession = Depends(get_db)
) -> Dict[str, Any]:
    """Dependency function for getting current user"""
    return await enhanced_auth_middleware.get_current_user(request, credentials, db)

async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Dependency function for getting current active user"""
    return await enhanced_auth_middleware.get_current_active_user(current_user)

async def verify_admin_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Dependency function for verifying admin user"""
    return await enhanced_auth_middleware.verify_admin_user(current_user)

async def verify_token_only(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> bool:
    """Dependency function for token verification only"""
    return await enhanced_auth_middleware.verify_token_only(credentials)
