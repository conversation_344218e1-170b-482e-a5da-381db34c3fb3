import secrets
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, Any

from sqlalchemy import or_, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from fastapi.responses import J<PERSON><PERSON>esponse

from app import app_logger
from app.core.config.settings import settings
from app.models.user import User, RefreshToken
from app.schemas.user import UserCreate as UserRegistrationRequest
from app.schemas.auth import UserLogin as UserLoginRequest
from app.schemas.user import UserResponse
from app.schemas.auth import (
    ForgotPasswordRequest, 
    ResetPasswordRequest, 
    AdminForgotPasswordRequest, 
    AdminResetPasswordRequest
)
from app.core.security.authentication_manager.jwt_handler import J<PERSON>THandler
from app.core.security.authentication_manager.password_handler import PasswordHandler
from app.core.utils.exception_manager.exception_handler import ExceptionHandler
from app.core.utils.system_constants.system_messages import client_messages, default_response
from app.core.responses.models import ErrorCodes
from app.core.api_standards import APIStandards
from fastapi import status


class AuthOperations:
    """Authentication operations class for handling user registration, login, and password management"""

    def __init__(self):
        self.jwt_handler = JWTHandler()
        self.password_handler = PasswordHandler()
        self.exception_handler = ExceptionHandler()
        # In-memory storage for password reset tokens (in production, use Redis or database)
        self.reset_tokens: Dict[str, Dict[str, Any]] = {}

    async def register_user(self, registration_data: UserRegistrationRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Register a new user

        Args:
            registration_data: User registration data
            db: Database session
            language: Response language

        Returns:
            JSONResponse: Registration response
        """
        response = default_response.copy()
        
        try:
            # Check if email already exists
            email_check = await db.execute(
                select(User).where(User.email == registration_data.email)
            )
            if email_check.scalar_one_or_none():
                return await self.exception_handler.manage_exception(
                    "email_already_exists",
                    language=language,
                    details={"email": registration_data.email}
                )
            
            # Check if user exists by mobile (if mobile is provided)
            if registration_data.mobile:
                stmt = select(User).where(User.mobile == registration_data.mobile)
                result = await db.execute(stmt)
                existing_user_by_mobile = result.scalar_one_or_none()
                
                if existing_user_by_mobile:
                    return await self.exception_handler.manage_exception(
                        "mobile_already_registered",
                        language=language,
                        details={"mobile": registration_data.mobile}
                    )
            
            # Validate password strength (basic validation)
            if len(registration_data.password) < 8:
                return JSONResponse(
                    content={
                        "success": False,
                        "message": {"title": "Password Policy Violation", "description": "Password must be at least 8 characters long"},
                        "data": {"details": {}},
                        "error_code": 4001
                    },
                    status_code=400
                )

            # Hash the password
            hashed_password = await self.password_handler.hash_password(registration_data.password)
            
            # Create new user
            new_user = User(
                id=uuid.uuid4(),
                first_name=registration_data.first_name,
                last_name=registration_data.last_name,
                email=registration_data.email,
                mobile=registration_data.mobile,
                password_hash=hashed_password,
                role='admin',
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            
            # Create user response
            user_response = UserResponse.model_validate(new_user)
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("user_registered_successfully"),
                "data": {"details": {"user": user_response.model_dump()}},
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"User registered successfully: {new_user.email}",
                function="AuthOperations-register_user"
            )
            
            return JSONResponse(content=response, status_code=201)
            
        except Exception as e:
            await db.rollback()
            await app_logger.exception(
                raw_exception=e,
                message_key="user_creation_failed",
                function="AuthOperations-register_user"
            )
            return await self.exception_handler.manage_exception(
                "user_creation_failed",
                language=language,
                details={"error": str(e)}
            )

    async def login_user(self, login_data: UserLoginRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Authenticate user login
        
        Args:
            login_data: User login data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Login response with JWT token
        """
        # response = default_response.copy()
        try:
            # Find user by email or mobile
            user_query = await db.execute(
                select(User).where(
                    or_(
                        User.email == login_data.email_or_mobile,
                        User.mobile == login_data.email_or_mobile
                    )
                )
            )
            user = user_query.scalar_one_or_none()
            if not user:
                return APIStandards.create_error_response(
                    error_message="No user found with the provided credentials",
                    error_title="User Not Found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    error_code=ErrorCodes.USER_NOT_FOUND
                )
            
            # Check if account is active
            if not user.is_active:
                return await self.exception_handler.manage_exception(
                    "account_disabled",
                    language=language,
                    details={"user_id": str(user.id)}
                )
            
            # Verify password
            is_valid = await self.password_handler.verify_password(login_data.password, user.password_hash)
            if not is_valid:
                return APIStandards.create_error_response(
                    error_message="Invalid email/mobile or password",
                    error_title="Invalid Credentials",
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    error_code=ErrorCodes.INVALID_USER_CREDENTIALS
                )
            
            # Handle remember_me token
            if login_data.remember_me:
                from app.core.security.remember_token_manager import RememberTokenManager
                remember_token_manager = RememberTokenManager()
                remember_me_token = remember_token_manager.generate_remember_token()
                
                # Store the remember me token with expiry
                token_stored = await remember_token_manager.store_remember_token(
                    str(user.id), 
                    remember_me_token, 
                    db
                )
                
                if not token_stored:
                    await app_logger.warning(
                        message=f"Failed to store remember token for user: {user.id}",
                        function="AuthOperations-login_user"
                    )
                    # Continue with login even if remember token storage fails
                    remember_me_token = None
                else:
                    await app_logger.info(
                        message=f"Successfully stored remember token for user: {user.id}",
                        function="AuthOperations-login_user"
                    )
            else:
                remember_me_token = None
            
            await db.commit()
            await db.refresh(user)

            # Generate JWT tokens
            token_data = {"user_id": str(user.id), "email": user.email}
            
            # Generate access token (15 minutes)
            access_token = self.jwt_handler.create_token(
                data=token_data, 
                token_type="access",
                remember_me=login_data.remember_me
            )
            
            # Generate refresh token (7 days)
            refresh_token = self.jwt_handler.create_token(
                data=token_data,
                token_type="refresh",
                remember_me=login_data.remember_me
            )
            
            # Store refresh token in database
            refresh_token_record = RefreshToken(
                user_id=user.id,
                token=refresh_token,
                expires_at=datetime.now(timezone.utc) + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
                is_active=True
            )
            db.add(refresh_token_record)
            await db.commit()
            
            # Debug: Log the refresh token storage
            await app_logger.info(
                message=f"Refresh token stored for user {user.id}: {refresh_token[:20]}...",
                function="AuthOperations-login_user"
            )
            
            # Calculate token expiration (in seconds)
            import jwt
            payload = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
            expires_in = int((payload["exp"] - datetime.now(timezone.utc).timestamp()))

            # Convert user response to dict with string UUID for JSON serialization
            user_response_data = UserResponse.model_validate(user).model_dump(mode='json')
            
            # Create login response data
            login_response_data = {
                "details": {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "expires_in": expires_in,
                    "user": user_response_data,
                    "remember_me_token": remember_me_token
                },
                "pagination": None
            }
            
            return APIStandards.create_success_response(
                data=login_response_data,
                message="User authenticated successfully.",
                title="Login Successful",
                status_code=status.HTTP_200_OK
            )
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="login_failed",
                function="AuthOperations-login_user"
            )
            return APIStandards.create_error_response(
                error_message="An error occurred during login",
                error_title="Login Failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )

    async def forgot_password(self, forgot_data: ForgotPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Initiate password reset process
        
        Args:
            forgot_data: Forgot password data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()
        
        try:
            # Find user by email or mobile number
            user_query = await db.execute(
                select(User).where(
                    or_(
                        User.email == forgot_data.identifier,
                        User.mobile == forgot_data.identifier
                    )
                )
            )
            user = user_query.scalar_one_or_none()
            
            if not user:
                return await self.exception_handler.manage_exception("user_not_found", language=language)
            
            # Generate reset token
            reset_token = secrets.token_urlsafe(32)
            
            # Store reset token in memory (in production, use Redis or database)
            self.reset_tokens[reset_token] = {
                "user_id": str(user.id),
                "created_at": datetime.utcnow(),
                "expires_at": datetime.utcnow().timestamp() + 3600  # 1 hour expiry
            }
            
            # In a real application, you would send an email here
            # For this demo, we'll just log the token
            await app_logger.info(
                message=f"Password reset token generated for {user.email}: {reset_token}",
                function="AuthOperations-forgot_password"
            )
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_email_sent"),
                "data": {"details": {"reset_token": reset_token, "expires_in": "1 hour"}},  # Remove this in production
                "error_code": 0
            })
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-forgot_password"
            )
            return await self.exception_handler.manage_exception("unknown_error", language=language)

    async def reset_password(self, reset_data: ResetPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Reset user password using token
        
        Args:
            reset_data: Reset password data
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()
        
        try:
            # Get token from in-memory storage
            token_info = self.reset_tokens.get(reset_data.token)
            if not token_info:
                return await self.exception_handler.manage_exception("invalid_reset_token", language=language)
            
            # Check if token has expired
            if datetime.utcnow() > token_info["expires_at"]:
                # Clean up expired token
                del self.reset_tokens[reset_data.token]
                return await self.exception_handler.manage_exception("invalid_reset_token", language=language)
            
            # Validate password strength
            if not self.password_handler.validate_password_strength(reset_data.new_password):
                return await self.exception_handler.manage_exception("password_policy_violation", language=language)
            
            # Find user
            user_query = await db.execute(select(User).where(User.email == token_info["email"]))
            user = user_query.scalar_one_or_none()
            
            if not user:
                return await self.exception_handler.manage_exception("user_not_found", language=language)
            
            # Hash new password
            hashed_password = await self.password_handler.hash_password(reset_data.new_password)
            
            # Update user password
            user.password_hash = hashed_password
            user.updated_at = datetime.utcnow()
            
            await db.commit()
            
            # Clean up used token
            del self.reset_tokens[reset_data.token]
            
            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_successfully"),
                "data": {"details": {}},
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"Password reset successfully for user: {user.email}",
                function="AuthOperations-reset_password"
            )
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await db.rollback()
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-reset_password"
            )
            return await self.exception_handler.manage_exception("unknown_error", language=language)

    async def admin_forgot_password(self, forgot_data: AdminForgotPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Initiate password reset process for admin users

        Args:
            forgot_data: Admin forgot password data
            db: Database session
            language: Response language

        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()

        try:
            # Find admin user by email or mobile number
            user_query = await db.execute(
                select(User).where(
                    or_(
                        User.email == forgot_data.identifier,
                        User.mobile == forgot_data.identifier
                    )
                )
            )
            user = user_query.scalar_one_or_none()

            if not user:
                return await self.exception_handler.manage_exception("user_not_found", language=language)

            # Verify user is admin
            if user.role != 'admin':
                return await self.exception_handler.manage_exception(
                    "insufficient_permissions",
                    language=language,
                    details={"message": "Only admin users can use this endpoint"}
                )

            # Check if user is active
            if not user.is_active:
                return await self.exception_handler.manage_exception(
                    "user_account_inactive",
                    language=language
                )

            # Generate reset token
            reset_token = secrets.token_urlsafe(32)
            reset_expires = datetime.utcnow() + timedelta(hours=1)  # 1 hour expiry

            # Store reset token in database
            user.password_reset_token = reset_token
            user.password_reset_expires = reset_expires
            user.updated_at = datetime.utcnow()

            await db.commit()

            # Send password reset email
            try:
                from app.services.email_service import EmailService
                email_service = EmailService()

                # Create reset link (in production, this would be your frontend URL)
                app_settings = settings
                frontend_url = getattr(app_settings, 'FRONTEND_URL', 'http://localhost:3000')
                reset_link = f"{frontend_url}/admin/reset-password?token={reset_token}"

                email_result = await email_service.send_template_email(
                    to_email=user.email,
                    template_name="password_reset",
                    template_data={
                        "name": user.first_name or "Admin",
                        "reset_link": reset_link
                    }
                )

                if email_result.get("status") == "sent":
                    await app_logger.info(
                        message=f"Admin password reset email sent to: {user.email}",
                        function="AuthOperations-admin_forgot_password"
                    )
                else:
                    await app_logger.warning(
                        message=f"Failed to send admin password reset email to: {user.email}",
                        function="AuthOperations-admin_forgot_password"
                    )

            except Exception as email_error:
                await app_logger.warning(
                    message=f"Email service error for admin password reset: {str(email_error)}",
                    function="AuthOperations-admin_forgot_password"
                )

            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_email_sent", "Password reset email sent"),
                "data": {
                    "email_sent": True,
                    "expires_in": "1 hour",
                    "email": user.email
                },
                "error_code": 0
            })

            await app_logger.info(
                message=f"Admin password reset initiated for: {user.email}",
                function="AuthOperations-admin_forgot_password"
            )

            return JSONResponse(content=response, status_code=200)

        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-admin_forgot_password"
            )
            return await self.exception_handler.manage_exception("unknown_error", language=language)

    async def admin_reset_password(self, reset_data: AdminResetPasswordRequest, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Reset admin user password using token

        Args:
            reset_data: Admin reset password data
            db: Database session
            language: Response language

        Returns:
            JSONResponse: Password reset response
        """
        response = default_response.copy()

        try:
            # Find user by reset token
            user_query = await db.execute(
                select(User).where(User.password_reset_token == reset_data.token)
            )
            user = user_query.scalar_one_or_none()

            if not user:
                return await self.exception_handler.manage_exception(
                    "invalid_reset_token",
                    language=language
                )

            # Verify user is admin
            if user.role != 'admin':
                return await self.exception_handler.manage_exception(
                    "insufficient_permissions",
                    language=language,
                    details={"message": "Only admin users can use this endpoint"}
                )

            # Check if token is expired
            if user.password_reset_expires and user.password_reset_expires < datetime.utcnow():
                # Clear expired token
                user.password_reset_token = None
                user.password_reset_expires = None
                await db.commit()

                return await self.exception_handler.manage_exception(
                    "reset_token_expired",
                    language=language
                )

            # Validate password strength
            if not self.password_handler.validate_password_strength(reset_data.new_password):
                return await self.exception_handler.manage_exception(
                    "password_policy_violation",
                    language=language
                )

            # Hash new password
            hashed_password = await self.password_handler.hash_password(reset_data.new_password)

            # Update user password and clear reset token
            user.password_hash = hashed_password
            user.password_reset_token = None
            user.password_reset_expires = None
            user.updated_at = datetime.utcnow()

            await db.commit()

            response.update({
                "success": True,
                "message": client_messages.get(language, {}).get("password_reset_successfully", "Password reset successfully"),
                "data": {
                    "password_updated": True,
                    "user_email": user.email
                },
                "error_code": 0
            })

            await app_logger.info(
                message=f"Admin password reset successfully for user: {user.email}",
                function="AuthOperations-admin_reset_password"
            )

            return JSONResponse(content=response, status_code=200)

        except Exception as e:
            await db.rollback()
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-admin_reset_password"
            )
            return await self.exception_handler.manage_exception("unknown_error", language=language)

    async def logout_user(self, user_id: str, db: AsyncSession, remember_me_token: str = None, language: str = "en") -> JSONResponse:
        """
        Logout user and clear remember me tokens
        
        Args:
            user_id: User ID from JWT token
            db: Database session
            remember_me_token: Remember me token to clear (optional)
            language: Response language
            
        Returns:
            JSONResponse: Logout response
        """
        try:
            # Clear remember me token if provided
            from app.core.security.remember_token_manager import RememberTokenManager
            remember_token_manager = RememberTokenManager()
            
            if remember_me_token:
                # Clear specific remember me token
                token_cleared = await remember_token_manager.clear_remember_token(
                    user_id=user_id, 
                    remember_me_token=remember_me_token,
                    db=db
                )
            else:
                # Clear all remember me tokens for this user
                token_cleared = await remember_token_manager.clear_all_remember_tokens(
                    user_id=user_id, 
                    db=db
                )
            
            if not token_cleared:
                await app_logger.warning(
                    message=f"Failed to clear remember token for user: {user_id}",
                    function="AuthOperations-logout_user"
                )
            
            # Invalidate all refresh tokens for this user
            await db.execute(
                update(RefreshToken)
                .where(RefreshToken.user_id == user_id)
                .values(is_active=False)
            )
            
            # Log the logout
            await app_logger.info(
                message=f"User logged out successfully: {user_id}",
                function="AuthOperations-logout_user"
            )
            
            return APIStandards.create_success_response(
                data={"logout_successful": True, "remember_token_cleared": token_cleared, "all_sessions_terminated": False},
                message="User logged out successfully. All sessions have been terminated.",
                title="Logout Successful",
                status_code=status.HTTP_200_OK
            )
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-logout_user"
            )
            return APIStandards.create_error_response(
                error_message="An error occurred during logout",
                error_title="Logout Failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )

    async def validate_token(self, current_user: User, language: str = "en") -> JSONResponse:
        """
        Validate JWT token and return user info
        
        Args:
            current_user: Current authenticated user
            language: Response language
            
        Returns:
            JSONResponse: Token validation response
        """
        response = default_response.copy()
        
        try:
            user_response = UserResponse.model_validate(current_user)
            
            response.update({
                "success": True,
                "message": {
                    "title": "Token Valid",
                    "description": "JWT token is valid and active"
                },
                "data": {
                    "details": {
                        "valid": True,
                        "user": user_response.dict()
                    }
                },
                "error_code": 0
            })
            
            await app_logger.info(
                message=f"Token validated for user: {current_user.email}",
                function="AuthOperations-validate_token"
            )
            
            return JSONResponse(content=response, status_code=200)
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-validate_token"
            )
            return await self.exception_handler.manage_exception("unknown_error", language=language)

    async def logout_all_devices(self, user_id: str, db: AsyncSession, language: str = "en") -> JSONResponse:
        """
        Logout user from all devices and clear remember me tokens
        
        Args:
            user_id: User ID from JWT token
            db: Database session
            language: Response language
            
        Returns:
            JSONResponse: Logout response
        """
        try:
            # Clear all remember me tokens for this user
            from app.core.security.remember_token_manager import RememberTokenManager
            remember_token_manager = RememberTokenManager()
            await remember_token_manager.clear_all_remember_tokens(
                user_id=user_id, 
                db=db
            )
            
            # Invalidate all refresh tokens for this user
            await db.execute(
                update(RefreshToken)
                .where(RefreshToken.user_id == user_id)
                .values(is_active=False)
            )
            
            # Log the logout
            await app_logger.info(
                message=f"User logged out from all devices: {user_id}",
                function="AuthOperations-logout_all_devices"
            )
            
            return APIStandards.create_success_response(
                data={"logout_successful": True, "all_sessions_terminated": True},
                message="User logged out from all devices successfully",
                title="Logout Successful",
                status_code=status.HTTP_200_OK
            )
            
        except Exception as e:
            await app_logger.exception(
                raw_exception=e,
                message_key="unknown_error",
                function="AuthOperations-logout_all_devices"
            )
            return APIStandards.create_error_response(
                error_message="An error occurred during logout",
                error_title="Logout Failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            ) 