"""
Franchisor service for business logic operations
"""

import uuid
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from datetime import datetime
from fastapi import HTTPException

from app.models.franchisor import Franchisor
from app.schemas.franchisor import (
    FranchisorCreateRequest,
    FranchisorUpdateRequest,
    FranchisorCategory,
    FranchisorRegion,
    FranchisorResponse
)
from app.core.logging import logger


class FranchisorService:
    """Service class for franchisor operations"""
    
    def __init__(self, db: AsyncSession):
        """Initialize franchisor service"""
        self.db = db
        try:
            from app.services.s3_service import S3Service
            self.s3_service = S3Service()
        except Exception as e:
            logger.warning(f"S3 service initialization failed: {e}")
            self.s3_service = None
    
    def _generate_franchisor_id(self) -> str:
        """Generate a unique franchisor ID"""
        return uuid.uuid4()
    
    async def _get_full_brochure_url(self, filename: str) -> Optional[str]:
        """Get full S3 URL for brochure filename"""
        if not filename:
            return None
        
        # If S3 service is not available, return the filename as is
        if not self.s3_service:
            logger.warning("S3 service not available, returning filename as is")
            return filename
        
        return self.s3_service.get_full_s3_url(filename)
    
    async def create_franchisor(self, franchisor_data: FranchisorCreateRequest) -> Franchisor:
        """Create a new franchisor"""
        try:
            franchisor = Franchisor(
                id=self._generate_franchisor_id(),
                name=franchisor_data.name,
                category=franchisor_data.category.value,
                region=franchisor_data.region.value if franchisor_data.region else None,
                budget=franchisor_data.budget,
                sub_category=franchisor_data.sub_category.value if franchisor_data.sub_category else None,
                is_active=franchisor_data.is_active
            )
            
            self.db.add(franchisor)
            await self.db.commit()
            await self.db.refresh(franchisor)
            
            logger.info(f"Created franchisor: {franchisor.id}")
            return franchisor
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating franchisor: {e}")
            raise
    
    async def get_franchisor_by_id(self, franchisor_id: str) -> Optional[Franchisor]:
        """Get franchisor by ID"""
        try:
            query = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(query)
            franchisor = result.scalar_one_or_none()
            
            # Convert brochure filename to full URL if it exists
            if franchisor and franchisor.brochure_url:
                franchisor.brochure_url = await self._get_full_brochure_url(franchisor.brochure_url)
            
            return franchisor
            
        except Exception as e:
            logger.error(f"Error retrieving franchisor {franchisor_id}: {e}")
            raise
    
    async def get_franchisors(
        self,
        skip: int = 0,
        limit: int = 20,
        category: Optional[str] = None,
        region: Optional[str] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> Tuple[List[FranchisorResponse], int]:
        """Get franchisors with pagination and filtering"""
        try:
            # Build query
            query = select(Franchisor)
            
            # Apply filters
            filters = []
            if category:
                filters.append(Franchisor.category == category)
            if region:
                filters.append(Franchisor.region == region)
            if is_active is not None:
                filters.append(Franchisor.is_active == is_active)
            if search:
                # Case-insensitive search by name
                filters.append(Franchisor.name.ilike(f"%{search}%"))
            
            if filters:
                query = query.where(and_(*filters))
            
            # Get total count
            count_query = select(func.count(Franchisor.id))
            if filters:
                count_query = count_query.where(and_(*filters))
            
            total_count = await self.db.scalar(count_query)
            
            # Apply pagination and ordering
            query = query.order_by(Franchisor.created_at.desc()).offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            franchisors = result.scalars().all()
            
            # Convert to list and process brochure URLs
            franchisor_list = []
            for franchisor in franchisors:
                # Convert to dict for processing
                franchisor_dict = {
                    "id": str(franchisor.id),
                    "name": franchisor.name,
                    "category": franchisor.category,
                    "region": franchisor.region,
                    "budget": franchisor.budget,
                    "sub_category": franchisor.sub_category,
                    "brochure_url": franchisor.brochure_url,
                    "is_active": franchisor.is_active,
                    "created_at": franchisor.created_at,
                    "updated_at": franchisor.updated_at
                }
                
                # Process brochure URL if S3 service is available
                if franchisor.brochure_url and self.s3_service:
                    try:
                        franchisor_dict["brochure_url"] = self.s3_service.get_full_s3_url(franchisor.brochure_url)
                    except Exception as e:
                        logger.warning(f"Failed to get full S3 URL for {franchisor.brochure_url}: {e}")
                        # Keep the original URL if S3 service fails
                
                franchisor_list.append(FranchisorResponse(**franchisor_dict))
            
            return franchisor_list, total_count
            
        except Exception as e:
            logger.error(f"Error retrieving franchisors: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve franchisors"
            )
    
    async def count_franchisors(
        self,
        category: Optional[FranchisorCategory] = None,
        region: Optional[FranchisorRegion] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> int:
        """Count franchisors with filters"""
        try:
            query = select(func.count(Franchisor.id))
            
            # Apply filters
            filters = []
            if category:
                filters.append(Franchisor.category == category.value)
            if region:
                filters.append(Franchisor.region == region.value)
            if is_active is not None:
                filters.append(Franchisor.is_active == is_active)
            if search:
                search_filter = or_(
                    Franchisor.name.ilike(f"%{search}%"),
                    Franchisor.category.ilike(f"%{search}%"),
                    Franchisor.region.ilike(f"%{search}%")
                )
                filters.append(search_filter)
            
            if filters:
                query = query.where(and_(*filters))
            
            result = await self.db.execute(query)
            return result.scalar()
            
        except Exception as e:
            logger.error(f"Error counting franchisors: {e}")
            raise
    
    async def update_franchisor(self, franchisor_id: str, franchisor_data: FranchisorUpdateRequest, brochure_file=None) -> Optional[Franchisor]:
        """Update franchisor with optional brochure upload"""
        try:
            query = select(Franchisor).where(Franchisor.id == franchisor_id)
            result = await self.db.execute(query)
            franchisor = result.scalar_one_or_none()
            if not franchisor:
                return None
            
            # Update basic fields
            if franchisor_data.name is not None:
                franchisor.name = franchisor_data.name
            if franchisor_data.category is not None:
                franchisor.category = franchisor_data.category.value
            if franchisor_data.region is not None:
                franchisor.region = franchisor_data.region.value
            if franchisor_data.budget is not None:
                franchisor.budget = franchisor_data.budget
            if franchisor_data.sub_category is not None:
                franchisor.sub_category = franchisor_data.sub_category.value
            if franchisor_data.is_active is not None:
                franchisor.is_active = franchisor_data.is_active
            
            # Handle brochure upload if provided
            if brochure_file and self.s3_service:
                try:
                    brochure_filename = await self.s3_service.upload_file(brochure_file, prefix="brochures")
                    franchisor.brochure_url = brochure_filename
                    logger.info(f"Uploaded brochure for franchisor {franchisor_id}: {brochure_filename}")
                except Exception as e:
                    logger.error(f"Failed to upload brochure for franchisor {franchisor_id}: {e}")
                    # Continue with update even if brochure upload fails
            
            franchisor.updated_at = datetime.utcnow()
            await self.db.commit()
            await self.db.refresh(franchisor)
            return franchisor
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating franchisor: {e}")
            raise
    
    async def delete_franchisor(self, franchisor_id: str) -> bool:
        """Delete franchisor"""
        try:
            # Check if franchisor exists
            existing_franchisor = await self.get_franchisor_by_id(franchisor_id)
            if not existing_franchisor:
                return False
            
            # Delete franchisor
            query = delete(Franchisor).where(Franchisor.id == franchisor_id)
            await self.db.execute(query)
            await self.db.commit()
            
            logger.info(f"Deleted franchisor: {franchisor_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting franchisor {franchisor_id}: {e}")
            raise
    
    async def update_brochure_url(self, franchisor_id: str, brochure_filename: str) -> Optional[Franchisor]:
        """Update franchisor brochure filename"""
        try:
            query = update(Franchisor).where(Franchisor.id == franchisor_id).values(
                brochure_url=brochure_filename,
                updated_at=datetime.utcnow()
            )
            await self.db.execute(query)
            await self.db.commit()
            
            return await self.get_franchisor_by_id(franchisor_id)
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating brochure URL for franchisor {franchisor_id}: {e}")
            raise
    
    async def bulk_create_franchisors(self, franchisors_data: List[Dict[str, Any]]) -> List[Franchisor]:
        """Bulk create franchisors"""
        try:
            franchisors = []
            for data in franchisors_data:
                franchisor = Franchisor(
                    id=self._generate_franchisor_id(),
                    name=data["name"],
                    category=data["category"],
                    region=data.get("region"),
                    budget=data.get("budget"),
                    sub_category=data.get("sub_category"),
                    is_active=data.get("is_active", True)
                )
                franchisors.append(franchisor)
            
            self.db.add_all(franchisors)
            await self.db.commit()
            
            # Refresh all franchisors
            for franchisor in franchisors:
                await self.db.refresh(franchisor)
            
            logger.info(f"Bulk created {len(franchisors)} franchisors")
            return franchisors
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error bulk creating franchisors: {e}")
            raise 