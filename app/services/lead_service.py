"""Lead service for business logic"""
import uuid
import csv
import io
from typing import List, Optional, Tuple, Dict, Any
from decimal import Decimal, InvalidOperation
from datetime import datetime
from sqlalchemy import select, update, and_, or_, desc, func
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, UploadFile

from app.models.lead import Lead, Communication
from app.schemas.lead import (
    LeadCreateRequest,
    LeadUpdateRequest,
    LeadResponse,
    LeadStatus
)
from app.core.logging import logger


class LeadService:
    """Service for lead-related business logic"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    def _generate_lead_id(self) -> str:
        """Generate a unique lead ID"""
        return str(uuid.uuid4())
    
    async def create_lead(self, lead_data: LeadCreateRequest, created_by: str = None) -> Lead:
        """Create a new lead"""
        try:
            # Prepare lead data, explicitly excluding created_at and updated_at
            lead_kwargs = dict(
                id=uuid.uuid4(),
                zoho_lead_id=lead_data.zoho_lead_id,
                full_name=lead_data.full_name,
                contact_number=lead_data.contact_number,
                email=lead_data.email,
                location=lead_data.location,
                lead_source=lead_data.lead_source,
                franchise_preference=lead_data.franchise_preference,
                budget_preference=lead_data.budget_preference,
                qualification_status=lead_data.qualification_status.value if lead_data.qualification_status else LeadStatus.NEW.value,
                is_active=lead_data.is_active if lead_data.is_active is not None else True,
                is_deleted=lead_data.is_deleted if lead_data.is_deleted is not None else False
            )
            # If the Lead model supports created_by, set it
            if hasattr(Lead, "created_by") and created_by:
                lead_kwargs["created_by"] = created_by
            lead = Lead(**lead_kwargs)
            self.db.add(lead)
            await self.db.commit()
            await self.db.refresh(lead)
            logger.info(f"Created lead: {lead.id}")
            return lead
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error creating lead: {e}")
            raise
    
    async def get_lead_by_id(self, lead_id: str, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Get a lead by ID with optional permission checking"""
        try:
            query = select(Lead).where(Lead.id == uuid.UUID(lead_id))
            result = await self.db.execute(query)
            lead = result.scalar_one_or_none()
            
            # Admin can see all leads, regular users can only see their assigned leads
            if lead and user_role and user_role != "ADMIN":
                # For now, allow all users to see all leads
                # TODO: Implement proper lead assignment logic
                pass
                
            return lead
        except Exception as e:
            logger.error(f"Error getting lead: {e}")
            raise
    
    async def get_leads(
        self,
        skip: int = 0,
        limit: int = 20,
        status: Optional[str] = None,
        search: Optional[str] = None,
        lead_source: Optional[str] = None,
        created_from: Optional[datetime] = None,
        created_to: Optional[datetime] = None,
        sort: Optional[str] = None,
        user_role: str = None,
        user_id: str = None
    ) -> Tuple[List[LeadResponse], int]:
        """Get all leads with pagination, filtering, and permission checking"""
        try:
            # Build query
            query = select(Lead)

            # Apply filters
            filters = []
            if status:
                filters.append(Lead.qualification_status == status)
            if lead_source:
                filters.append(Lead.lead_source.ilike(f"%{lead_source}%"))
            if created_from:
                filters.append(Lead.created_at >= created_from)
            if created_to:
                filters.append(Lead.created_at <= created_to)
            if search:
                # Enhanced full-text search across multiple fields
                search_filter = or_(
                    Lead.full_name.ilike(f"%{search}%"),
                    Lead.email.ilike(f"%{search}%"),
                    Lead.contact_number.ilike(f"%{search}%"),
                    Lead.location.ilike(f"%{search}%"),
                    Lead.franchise_preference.ilike(f"%{search}%")
                )
                filters.append(search_filter)

            if filters:
                query = query.where(and_(*filters))
            
            # Get total count
            count_query = select(func.count(Lead.id))
            if filters:
                count_query = count_query.where(and_(*filters))
            
            total_count = await self.db.scalar(count_query)
            
            # Apply sorting
            if sort:
                if sort == "name_asc":
                    query = query.order_by(Lead.full_name.asc())
                elif sort == "name_desc":
                    query = query.order_by(Lead.full_name.desc())
                elif sort == "created_asc":
                    query = query.order_by(Lead.created_at.asc())
                elif sort == "created_desc":
                    query = query.order_by(Lead.created_at.desc())
                else:
                    query = query.order_by(desc(Lead.created_at))
            else:
                query = query.order_by(desc(Lead.created_at))

            # Apply pagination
            query = query.offset(skip).limit(limit)
            
            # Execute query
            result = await self.db.execute(query)
            leads = result.scalars().all()
            
            # Convert to response models
            lead_responses = []
            for lead in leads:
                lead_response = LeadResponse(
                    id=str(lead.id),
                    zoho_lead_id=lead.zoho_lead_id,
                    full_name=lead.full_name,
                    contact_number=lead.contact_number,
                    email=lead.email,
                    location=lead.location,
                    lead_source=lead.lead_source,
                    franchise_preference=lead.franchise_preference,
                    budget_preference=lead.budget_preference,
                    qualification_status=LeadStatus(lead.qualification_status),
                    created_at=lead.created_at,
                    updated_at=lead.updated_at
                )
                lead_responses.append(lead_response)
            
            return lead_responses, total_count
            
        except Exception as e:
            logger.error(f"Error getting leads: {e}")
            raise HTTPException(
                status_code=500,
                detail="Failed to retrieve leads"
            )
    
    async def update_lead(self, lead_id: str, lead_data: LeadUpdateRequest, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Update a lead with permission checking"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return None
            
            # Prepare update data, excluding None values
            update_data = {}
            if lead_data.full_name is not None:
                update_data["full_name"] = lead_data.full_name
            if lead_data.contact_number is not None:
                update_data["contact_number"] = lead_data.contact_number
            if lead_data.email is not None:
                update_data["email"] = lead_data.email
            if lead_data.location is not None:
                update_data["location"] = lead_data.location
            if lead_data.lead_source is not None:
                update_data["lead_source"] = lead_data.lead_source
            if lead_data.franchise_preference is not None:
                update_data["franchise_preference"] = lead_data.franchise_preference
            if lead_data.budget_preference is not None:
                update_data["budget_preference"] = lead_data.budget_preference
            if lead_data.qualification_status is not None:
                update_data["qualification_status"] = lead_data.qualification_status.value
            
            if update_data:
                await self.db.execute(
                    update(Lead)
                    .where(Lead.id == uuid.UUID(lead_id))
                    .values(**update_data)
                )
                await self.db.commit()
            
            return await self.get_lead_by_id(lead_id, user_role, user_id)
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error updating lead: {e}")
            raise
    
    async def delete_lead(self, lead_id: str, user_role: str = None, user_id: str = None) -> bool:
        """Delete a lead with permission checking"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return False
            
            await self.db.delete(lead)
            await self.db.commit()
            return True
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Error deleting lead: {e}")
            raise
    
    async def search_leads(
        self,
        query: str,
        skip: int = 0,
        limit: int = 20
    ) -> List[LeadResponse]:
        """Search leads by query"""
        try:
            search_query = select(Lead).where(
                or_(
                    Lead.full_name.ilike(f"%{query}%"),
                    Lead.email.ilike(f"%{query}%"),
                    Lead.contact_number.ilike(f"%{query}%"),
                    Lead.location.ilike(f"%{query}%")
                )
            ).offset(skip).limit(limit).order_by(desc(Lead.created_at))
            
            result = await self.db.execute(search_query)
            leads = result.scalars().all()
            
            # Convert to response models
            lead_responses = []
            for lead in leads:
                lead_response = LeadResponse(
                    id=str(lead.id),
                    zoho_lead_id=lead.zoho_lead_id,
                    full_name=lead.full_name,
                    contact_number=lead.contact_number,
                    email=lead.email,
                    location=lead.location,
                    lead_source=lead.lead_source,
                    franchise_preference=lead.franchise_preference,
                    budget_preference=lead.budget_preference,
                    qualification_status=LeadStatus(lead.qualification_status),
                    created_at=lead.created_at,
                    updated_at=lead.updated_at
                )
                lead_responses.append(lead_response)
            
            return lead_responses
        except Exception as e:
            logger.error(f"Error searching leads: {e}")
            raise
    
    async def assign_lead(self, lead_id: str, assigned_to: str) -> Optional[Lead]:
        """Assign a lead to a user"""
        try:
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                return None
            
            # TODO: Add assigned_to field to Lead model if needed
            # For now, just return the lead
            return lead
        except Exception as e:
            logger.error(f"Error assigning lead: {e}")
            raise
    
    async def update_lead_score(self, lead_id: str, score: int, user_role: str = None, user_id: str = None) -> Optional[Lead]:
        """Update lead score"""
        try:
            lead = await self.get_lead_by_id(lead_id, user_role, user_id)
            if not lead:
                return None
            
            # TODO: Add score field to Lead model if needed
            # For now, just return the lead
            return lead
        except Exception as e:
            logger.error(f"Error updating lead score: {e}")
            raise

    async def bulk_upload_leads(self, file: UploadFile, created_by: str = None) -> Dict[str, Any]:
        """Bulk upload leads from CSV file with duplicate detection"""
        try:
            # Validate file type
            if not file.filename or not file.filename.endswith('.csv'):
                raise HTTPException(status_code=400, detail="File must be a CSV file")

            # Read file content
            content = await file.read()
            csv_content = content.decode('utf-8')

            # Parse CSV
            csv_reader = csv.DictReader(io.StringIO(csv_content))

            # Required headers (only full_name and contact_number are truly required)
            required_headers = {'full_name', 'contact_number'}

            # All expected headers (including optional ones)
            expected_headers = {
                'full_name', 'contact_number', 'email', 'location',
                'lead_source', 'franchise_preference', 'budget_preference',
                'qualification_status', 'is_active', 'is_deleted'
            }

            # Validate required headers
            if not required_headers.issubset(set(csv_reader.fieldnames or [])):
                missing_headers = required_headers - set(csv_reader.fieldnames or [])
                raise HTTPException(
                    status_code=422,
                    detail=f"Missing required headers: {', '.join(missing_headers)}"
                )

            leads_to_create = []
            duplicates = []
            errors = []
            processed_contacts = set()
            processed_emails = set()

            # Check existing leads for duplicate detection
            existing_contacts_query = select(Lead.contact_number).where(Lead.contact_number.isnot(None))
            existing_emails_query = select(Lead.email).where(Lead.email.isnot(None))

            existing_contacts_result = await self.db.execute(existing_contacts_query)
            existing_emails_result = await self.db.execute(existing_emails_query)

            existing_contacts = {row[0] for row in existing_contacts_result.fetchall()}
            existing_emails = {row[0] for row in existing_emails_result.fetchall()}

            row_number = 1
            for row in csv_reader:
                row_number += 1
                try:
                    # Trim whitespace from all fields
                    row = {k: v.strip() if v else None for k, v in row.items()}

                    # Validate required fields
                    if not row.get('full_name') or not row.get('contact_number'):
                        errors.append(f"Row {row_number}: Missing required fields (full_name, contact_number)")
                        continue

                    # Check for duplicates within file
                    contact_number = row['contact_number']
                    email = row.get('email')

                    if contact_number in processed_contacts or contact_number in existing_contacts:
                        duplicates.append(f"Row {row_number}: Duplicate contact number {contact_number}")
                        continue

                    if email and (email in processed_emails or email in existing_emails):
                        duplicates.append(f"Row {row_number}: Duplicate email {email}")
                        continue

                    # Convert budget_preference to Decimal
                    budget_preference = None
                    if row.get('budget_preference'):
                        try:
                            budget_preference = Decimal(str(row['budget_preference']))
                        except (InvalidOperation, ValueError):
                            errors.append(f"Row {row_number}: Invalid budget_preference value")
                            continue

                    # Convert boolean fields
                    is_active = True  # Default value
                    if row.get('is_active'):
                        is_active_str = str(row['is_active']).lower().strip()
                        if is_active_str in ['true', '1', 'yes', 'y']:
                            is_active = True
                        elif is_active_str in ['false', '0', 'no', 'n']:
                            is_active = False
                        else:
                            errors.append(f"Row {row_number}: Invalid is_active value (use true/false, 1/0, yes/no)")
                            continue

                    is_deleted = False  # Default value
                    if row.get('is_deleted'):
                        is_deleted_str = str(row['is_deleted']).lower().strip()
                        if is_deleted_str in ['true', '1', 'yes', 'y']:
                            is_deleted = True
                        elif is_deleted_str in ['false', '0', 'no', 'n']:
                            is_deleted = False
                        else:
                            errors.append(f"Row {row_number}: Invalid is_deleted value (use true/false, 1/0, yes/no)")
                            continue

                    # Create lead object
                    lead = Lead(
                        id=uuid.uuid4(),
                        full_name=row['full_name'],
                        contact_number=contact_number,
                        email=email,
                        location=row.get('location'),
                        lead_source=row.get('lead_source'),
                        franchise_preference=row.get('franchise_preference'),
                        budget_preference=budget_preference,
                        qualification_status=row.get('qualification_status', 'new'),
                        is_active=is_active,
                        is_deleted=is_deleted
                    )

                    leads_to_create.append(lead)
                    processed_contacts.add(contact_number)
                    if email:
                        processed_emails.add(email)

                except Exception as e:
                    errors.append(f"Row {row_number}: {str(e)}")
                    continue

            # Bulk save leads
            if leads_to_create:
                self.db.add_all(leads_to_create)
                await self.db.commit()

                # Refresh all leads to get the created_at timestamps
                for lead in leads_to_create:
                    await self.db.refresh(lead)

            result = {
                "total_processed": row_number - 1,
                "successful_imports": len(leads_to_create),
                "duplicates_found": len(duplicates),
                "errors_found": len(errors),
                "duplicates": duplicates,
                "errors": errors
            }

            logger.info(f"Bulk upload completed: {result}")
            return result

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in bulk upload: {e}")
            await self.db.rollback()
            raise HTTPException(status_code=500, detail=f"Bulk upload failed: {str(e)}")

    async def get_lead_communications(self, lead_id: str) -> List[Dict[str, Any]]:
        """Get communication history for a lead"""
        try:
            # Validate lead exists
            lead = await self.get_lead_by_id(lead_id)
            if not lead:
                raise HTTPException(status_code=404, detail="Lead not found")

            # Query communications
            query = select(Communication).where(Communication.lead_id == uuid.UUID(lead_id)).order_by(desc(Communication.created_at))
            result = await self.db.execute(query)
            communications = result.scalars().all()

            # Convert to response format
            communication_list = []
            for comm in communications:
                communication_list.append({
                    "id": str(comm.id),
                    "communication_type": comm.communication_type,
                    "subject": comm.subject,
                    "content": comm.content,
                    "direction": comm.direction,
                    "user_id": str(comm.user_id) if comm.user_id else None,
                    "created_at": comm.created_at,
                    "updated_at": comm.updated_at
                })

            return communication_list

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting lead communications: {e}")
            raise HTTPException(status_code=500, detail="Failed to retrieve communication history")
