import logging
import os
from logging.handlers import RotatingFileHandler

# Define the log directory
log_directory = './logs'

# Create the directory if it doesn't exist
if not os.path.exists(log_directory):
    os.makedirs(log_directory)

# Configure RotatingFileHandler
log_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s')

log_handler = RotatingFileHandler(
    os.path.join(log_directory, 'app.log'),
    maxBytes=1 * 1024 * 1024,  # 1 MB
    backupCount=10  # Keep last 10 backup files
)

log_handler.setFormatter(log_formatter)
log_handler.setLevel(logging.INFO)

# Set up main logger
logger = logging.getLogger("growthhive_logger")
logger.setLevel(logging.INFO)
logger.addHandler(log_handler)

# Optional: also log to console
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)
console_handler.setLevel(logging.INFO)
logger.addHandler(console_handler)


class AppLogger:
    """Application logger for Growth Hive Auth API"""
    
    def __init__(self):
        self.logger = logger
    
    async def info(self, message: str, function: str = ""):
        """Log info message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.info(log_message)
    
    async def warning(self, message: str, function: str = ""):
        """Log warning message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.warning(log_message)
    
    async def error(self, message: str, function: str = ""):
        """Log error message"""
        log_message = f"[{function}] {message}" if function else message
        self.logger.error(log_message)
    
    async def exception(self, raw_exception: Exception, message_key: str, function: str = ""):
        """Log exception"""
        log_message = f"[{function}] {message_key}: {str(raw_exception)}" if function else f"{message_key}: {str(raw_exception)}"
        self.logger.exception(log_message)


# Global app logger instance
app_logger = AppLogger()


class LoggerUtility:
    _instance = None

    def __new__(cls, *args, **kwargs):
        """ Create singleton instance """
        if not cls._instance:
            cls._instance = super(LoggerUtility, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    @staticmethod
    async def exception(raw_exception, message_key: str, function: str):
        """ Log exception """
        logger.exception(
            msg=f"[EXCEPTION] message_key: {message_key}  \n "
                f"class-function: {function} \n exception: {str(raw_exception)}"
        )

    @staticmethod
    async def error(message_key: str, function: str, extra_message: str = None):
        """ Log error """
        logger.error(
            msg=f"[ERROR] message_key: {message_key} \n "
                f"class-function: {function} \n message: {extra_message or 'No additional message'}"
        )

    @staticmethod
    async def warning(message: str, function: str):
        """ Log warning """
        logger.warning(f"[WARNING] class-function: {function} | message: {message}")

    @staticmethod
    async def info(message: str, function: str):
        """ Log info """
        logger.info(f"[INFO] class-function: {function} | message: {message}")

    @staticmethod
    async def debug(message: str, function: str):
        """ Log debug """
        logger.debug(f"[DEBUG] class-function: {function} | message: {message}")

    @staticmethod
    async def critical(message: str, function: str):
        """ Log critical """
        logger.critical(f"[CRITICAL] class-function: {function} | message: {message}")


# Singleton instances
app_logger = LoggerUtility() 