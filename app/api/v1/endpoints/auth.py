"""
Authentication API endpoints with remember me functionality
"""
from fastapi import APIRouter, Depends, status, Request, Query, Form
from fastapi.responses import JSONResponse
from fastapi.security import HTTPBearer
from datetime import datetime, timedelta, timezone
from jose import jwt
from passlib.context import CryptContext
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

from app.core.database.connection import get_db
from app.core.config.settings import settings
from app.schemas.user import (
    UserLoginRequest,
    UserLoginResponse,
    UserResponse,
    UserRegistrationRequest,
    RememberMeLoginRequest,
    LogoutResponse,
    RefreshTokenResponse
)
from app.models.user import User, UserRole, RefreshToken
from app.schemas.base_response import StandardResponse
from app.core.utils.logger.logger import logger
from app.core.responses.models import ErrorCodes
from app.core.api_standards import APIStandards
from app.core.operations.auth.auth_operations import AuthOperations
# Import enhanced auth middleware
from app.core.security.enhanced_auth_middleware import get_current_user

router = APIRouter()

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBearer(auto_error=False)

def verify_password(plain_password, hashed_password):
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def create_access_token(data: dict, remember_me: bool = False):
    """Create JWT access token with remember me option"""
    to_encode = data.copy()
    if remember_me:
        expire = datetime.utcnow() + timedelta(days=30)  # 30 days for remember me
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)  # 15 minutes for normal session
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)
    return encoded_jwt, expire

@router.post(
    "/register",
    response_model=StandardResponse[UserResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Register a new user",
    description="Register a new user with email, password, and optional profile information. Role is automatically set to ADMIN.",
    responses={
        201: {
            "description": "User successfully registered",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "User registered successfully",
                        "data": {
                            "id": 1,
                            "email": "<EMAIL>",
                            "first_name": "John",
                            "last_name": "Doe",
                            "mobile": "+1234567890",
                            "role": "ADMIN",
                            "is_active": True,
                            "created_at": "2024-03-20T10:00:00",
                            "updated_at": "2024-03-20T10:00:00"
                        }
                    }
                }
            }
        },
        409: {
            "description": "Email already registered",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "Email already registered",
                        "data": None
                    }
                }
            }
        },
        422: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character",
                        "data": None
                    }
                }
            }
        }
    }
)
async def register_user(
    user_data: UserRegistrationRequest,
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """
    Register a new user with enhanced validation and logging.
    Role is automatically set to ADMIN for all new registrations.
    
    Args:
        user_data: User registration data (role is not required, defaults to ADMIN)
        db: Database session
        
    Returns:
        JSONResponse containing the created user data
        
    Raises:
        HTTPException: If email already exists or validation fails
    """
    
    try:
        # Log registration attempt
        logger.info(
            f"Registration attempt for email: {user_data.email}",
            extra={
                "email": user_data.email,
                "role": "ADMIN",  # Role is always ADMIN for new registrations
                "has_mobile": bool(user_data.mobile)
            }
        )
        
        # Check if user exists by email
        stmt = select(User).where(User.email == user_data.email)
        result = await db.execute(stmt)
        existing_user_by_email = result.scalar_one_or_none()
        
        if existing_user_by_email:
            return APIStandards.create_error_response(
                error_message="This email address is already registered in our system",
                error_title="Email Already Registered",
                status_code=status.HTTP_409_CONFLICT,
                error_code=ErrorCodes.EMAIL_TAKEN
            )
        
        # Check if user exists by mobile (if mobile is provided)
        if user_data.mobile:
            stmt = select(User).where(User.mobile == user_data.mobile)
            result = await db.execute(stmt)
            existing_user_by_mobile = result.scalar_one_or_none()
            
            if existing_user_by_mobile:
                return APIStandards.create_error_response(
                    error_message="This mobile number is already registered in our system",
                    error_title="Mobile Number Already Registered",
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=ErrorCodes.MOBILE_TAKEN
                )
        
        # Create new user
        new_user = User(
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),  # Hash the password before saving
            first_name=user_data.first_name,
            last_name=user_data.last_name,
            mobile=user_data.mobile,
            role=UserRole.ADMIN  # Always set to ADMIN for new registrations
        )
        
        try:
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            
            # Log successful registration
            logger.info(
                f"User registered successfully: {new_user.id}",
                extra={
                    "user_id": new_user.id,
                    "email": new_user.email,
                    "role": new_user.role
                }
            )

            # Convert user response to dict with string UUID for JSON serialization
            await db.refresh(new_user)  # Refresh to ensure all attributes are loaded
            user_response_data = {
                "id": str(new_user.id),
                "email": new_user.email,
                "first_name": new_user.first_name,
                "last_name": new_user.last_name,
                "mobile": new_user.mobile,
                "role": new_user.role.value if hasattr(new_user.role, 'value') else new_user.role,
                "is_active": new_user.is_active,
                "created_at": new_user.created_at.isoformat() if new_user.created_at else None,
                "updated_at": new_user.updated_at.isoformat() if new_user.updated_at else None
            }
            
            return APIStandards.create_success_response(
                data=user_response_data,
                message="Your account has been created.",
                title="User registered successfully",
                status_code=status.HTTP_201_CREATED
            )
            
        except IntegrityError as e:
            await db.rollback()
            logger.error(f"Database integrity error during registration for {user_data.email}: {e}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="A database integrity error occurred.",
                error_title="Registration Failed",
                status_code=status.HTTP_409_CONFLICT,
                error_code=ErrorCodes.DUPLICATE_ENTRY
            )
        except Exception as e:
            await db.rollback()
            logger.error(f"An unexpected error occurred during registration for {user_data.email}: {e}", exc_info=True)
            return APIStandards.create_error_response(
                error_message="An error occurred during registration",
                error_title="Registration Failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.CREATION_FAILED
            )

    except Exception as e:
        logger.error(f"An unexpected error occurred in register_user for {user_data.email}: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An error occurred during registration",
            error_title="Registration Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.post("/login", response_model=StandardResponse[UserLoginResponse])
async def login(
    login_data: UserLoginRequest,
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """Login user with email or mobile number. If remember_me is true, returns a persistent remember_me_token."""
    auth_ops = AuthOperations()
    return await auth_ops.login_user(login_data, db)

@router.get("/me", response_model=StandardResponse[UserResponse])
async def get_current_user_info(
    current_user: dict = Depends(get_current_user)
) -> JSONResponse:
    if isinstance(current_user, JSONResponse):
        return current_user
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="User Info Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    return APIStandards.create_success_response(
        data=UserResponse.model_validate(user).model_dump(mode='json'),
        message="User information retrieved successfully.",
        title="User Information"
    )

@router.post(
    "/logout", 
    response_model=StandardResponse[LogoutResponse],
    summary="Logout user",
    description="Logout user and clear sessions. Use 'logout_all=true' query parameter to logout from all devices.",
    responses={
        200: {
            "description": "Logout successful",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": "User logged out successfully. All sessions have been terminated.",
                        "data": {
                            "logout_successful": True,
                            "remember_token_cleared": True,
                            "all_sessions_terminated": True
                        }
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": "Authentication required",
                        "data": None
                    }
                }
            }
        }
    }
)
async def logout(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    logout_all: bool = Query(False, description="Whether to logout from all devices")
) -> JSONResponse:
    """
    Logout user and clear sessions.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        logout_all: Whether to logout from all devices (default: False)
        
    Returns:
        JSONResponse: Logout response
    """
    if isinstance(current_user, JSONResponse):
        return current_user
    
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="Logout Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    
    try:
        from app.core.security.remember_token_manager import RememberTokenManager
        remember_token_manager = RememberTokenManager()
        
        if logout_all:
            # Logout from all devices
            tokens_revoked = await remember_token_manager.revoke_all_remember_tokens(str(user.id), db)
            if not tokens_revoked:
                return APIStandards.create_error_response(
                    error_message="Failed to revoke all sessions",
                    error_title="Logout Failed",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_code=ErrorCodes.UNKNOWN_ERROR
                )
            
            return APIStandards.create_success_response(
                data={
                    "logout_successful": True, 
                    "remember_token_cleared": True,
                    "all_sessions_terminated": True
                },
                message="Successfully logged out from all devices. All sessions have been terminated.",
                title="Logout from All Devices Successful",
                status_code=status.HTTP_200_OK
            )
        else:
            # Single logout - use existing AuthOperations
            auth_ops = AuthOperations()
            return await auth_ops.logout_user(str(user.id), db)
            
    except Exception as e:
        logger.error(f"Error during logout: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An error occurred during logout",
            error_title="Logout Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.post("/refresh", response_model=StandardResponse[UserLoginResponse])
async def refresh_token(
    refresh_token: str = Form(..., description="Refresh token to exchange for new access token"),
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """
    Refresh access token using refresh token
    
    Args:
        refresh_token: The refresh token to exchange
        db: Database session
        
    Returns:
        JSONResponse: New access token and user data
    """
    try:
        # Import JWT handler
        from app.core.security.authentication_manager.jwt_handler import JWTHandler
        from app.models.user import RefreshToken
        jwt_handler = JWTHandler()
        
        # Validate refresh token
        try:
            payload = await jwt_handler.decode_token(refresh_token)
        except Exception:
            return APIStandards.create_error_response(
                error_message="Invalid or expired refresh token",
                error_title="Invalid Refresh Token",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.INVALID_ACCESS_TOKEN
            )
        
        # Check if token type is refresh
        if payload.get("type") != "refresh":
            return APIStandards.create_error_response(
                error_message="Invalid token type. Expected refresh token.",
                error_title="Invalid Token Type",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.INVALID_ACCESS_TOKEN
            )
        
        # Check if refresh token exists in database and is active
        from sqlalchemy import select
        
        token_query = await db.execute(
            select(RefreshToken).where(
                RefreshToken.token == refresh_token,
                RefreshToken.is_active,
                RefreshToken.expires_at > datetime.utcnow()
            )
        )
        stored_token = token_query.scalar_one_or_none()
        
        if not stored_token:
            return APIStandards.create_error_response(
                error_message="Refresh token not found or expired",
                error_title="Invalid Refresh Token",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.INVALID_ACCESS_TOKEN
            )
        
        # Get user
        user_query = await db.execute(
            select(User).where(User.id == payload.get("user_id"))
        )
        user = user_query.scalar_one_or_none()
        
        if not user or not user.is_active:
            return APIStandards.create_error_response(
                error_message="User not found or inactive",
                error_title="User Not Found",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.USER_NOT_FOUND
            )
        
        # Generate new access token
        token_data = {"user_id": str(user.id), "email": user.email}
        new_access_token = jwt_handler.create_token(
            data=token_data,
            token_type="access",
            remember_me=payload.get("remember_me", False)
        )
        
        # Calculate new token expiration
        new_payload = jwt.decode(new_access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        expires_in = int((new_payload["exp"] - datetime.now(timezone.utc).timestamp()))
        
        # Convert user response to dict
        await db.refresh(user)  # Refresh to ensure all attributes are loaded
        user_response_data = {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "mobile": user.mobile,
            "role": user.role.value if hasattr(user.role, 'value') else user.role,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }
        
        # Create response data
        refresh_response_data = {
            "details": {
                "access_token": new_access_token,
                "refresh_token": refresh_token,  # Return same refresh token
                "token_type": "bearer",
                "expires_in": expires_in,
                "user": user_response_data,
                "remember_me_token": None  # Not applicable for refresh
            },
            "pagination": None
        }
        
        return APIStandards.create_success_response(
            data=refresh_response_data,
            message="Access token refreshed successfully.",
            title="Token Refreshed",
            status_code=status.HTTP_200_OK
        )
        
    except Exception as e:
        logger.error(f"Error refreshing token: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An error occurred while refreshing the token",
            error_title="Refresh Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.post("/refresh-remember-token", response_model=StandardResponse[RefreshTokenResponse])
async def refresh_remember_token(
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    if isinstance(current_user, JSONResponse):
        return current_user
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="Token Refresh Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    try:
        from app.core.security.remember_token_manager import RememberTokenManager
        remember_token_manager = RememberTokenManager()
        new_token = await remember_token_manager.rotate_remember_token(str(user.id), db)
        if not new_token:
            return APIStandards.create_error_response(
                error_message="Failed to refresh remember me token",
                error_title="Token Refresh Failed",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                error_code=ErrorCodes.UNKNOWN_ERROR
            )
        return APIStandards.create_success_response(
            data={"token_refreshed": True, "new_remember_me_token": new_token},
            message="Remember me token has been refreshed successfully.",
            title="Token Refresh Successful",
            status_code=status.HTTP_200_OK
        )
    except Exception as e:
        logger.error(f"Error during remember token refresh: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An error occurred during token refresh",
            error_title="Token Refresh Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

@router.post("/remember-me-login", response_model=StandardResponse[UserLoginResponse])
async def remember_me_login(
    request: RememberMeLoginRequest,
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """Authenticate using remember_me_token and return a new JWT and user info."""
    try:
        from app.core.security.remember_token_manager import RememberTokenManager
        from app.core.security.authentication_manager.jwt_handler import JWTHandler
        from app.core.config.settings import settings
        import jwt
        from datetime import datetime

        # Validate remember me token format
        remember_token_manager = RememberTokenManager()
        if not remember_token_manager.validate_remember_token_format(request.remember_me_token):
            return APIStandards.create_error_response(
                error_message="Invalid remember me token format",
                error_title="Authentication Failed",
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=ErrorCodes.INVALID_TOKEN_FORMAT
            )

        # Authenticate user with remember me token
        user = await remember_token_manager.authenticate_with_remember_token(request.remember_me_token, db)
        if not user:
            return APIStandards.create_error_response(
                error_message="Invalid or expired remember me token",
                error_title="Authentication Failed",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.INVALID_USER_CREDENTIALS
            )

        # Check if user is active
        if not user.is_active:
            return APIStandards.create_error_response(
                error_message="User account is inactive",
                error_title="Authentication Failed",
                status_code=status.HTTP_401_UNAUTHORIZED,
                error_code=ErrorCodes.ACCOUNT_DISABLED
            )

        # Issue new JWT
        jwt_handler = JWTHandler()
        token_data = {"user_id": str(user.id), "email": user.email}
        access_token = jwt_handler.create_token(data=token_data, token_type="access", remember_me=True)
        refresh_token = jwt_handler.create_token(data=token_data, token_type="refresh", remember_me=True)
        
        # Store refresh token in database
        refresh_token_record = RefreshToken(
            user_id=user.id,
            token=refresh_token,
            expires_at=datetime.now(timezone.utc) + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS),
            is_active=True
        )
        db.add(refresh_token_record)
        await db.commit()
        
        payload = jwt.decode(access_token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        expires_in = int((payload["exp"] - datetime.now(timezone.utc).timestamp()))

        # Convert user response to dict with string UUID for JSON serialization
        await db.refresh(user)  # Refresh to ensure all attributes are loaded
        user_response_data = {
            "id": str(user.id),
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "mobile": user.mobile,
            "role": user.role.value if hasattr(user.role, 'value') else user.role,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }
        
        # Create login response data
        login_response_data = {
            "details": {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "expires_in": expires_in,
                "user": user_response_data,
                "remember_me_token": user.remember_me_token
            },
            "pagination": None
        }
        
        logger.info(f"Remember me login successful for user: {user.email}")
        
        return APIStandards.create_success_response(
            data=login_response_data,
            message="Authenticated via remember me token",
            title="Login Successful",
            status_code=status.HTTP_200_OK
        )
        
    except jwt.ExpiredSignatureError:
        return APIStandards.create_error_response(
            error_message="Remember me token has expired",
            error_title="Authentication Failed",
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code=ErrorCodes.TOKEN_EXPIRED
        )
    except jwt.InvalidTokenError:
        return APIStandards.create_error_response(
            error_message="Invalid remember me token",
            error_title="Authentication Failed",
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code=ErrorCodes.INVALID_TOKEN_FORMAT
        )
    except Exception as e:
        logger.error(f"Error during remember me login: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message="An error occurred during authentication",
            error_title="Authentication Failed",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )

# Test endpoints for dual-token authentication system
@router.get(
    "/auth-test/protected",
    summary="Test Protected Endpoint",
    description="Test endpoint that requires authentication via JWT or remember me token",
    response_model=StandardResponse[dict]
)
async def test_protected_endpoint(
    current_user: dict = Depends(get_current_user)
) -> JSONResponse:
    if isinstance(current_user, JSONResponse):
        return current_user
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="Authentication Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    return APIStandards.create_success_response(
        data={
            "message": "Access granted to protected endpoint",
            "user_id": str(user.id),
            "email": user.email,
            "auth_method": current_user.get("auth_method", "unknown")
        },
        message="Successfully accessed protected endpoint",
        title="Protected Endpoint Test"
    )

@router.get(
    "/auth-test/auth-info",
    summary="Get Authentication Information",
    description="Get detailed information about the current authentication method",
    response_model=StandardResponse[dict]
)
async def get_auth_info(
    request: Request,
    current_user: dict = Depends(get_current_user)
) -> JSONResponse:
    if isinstance(current_user, JSONResponse):
        return current_user
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="Authentication Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    auth_method = current_user.get("auth_method", "unknown")
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        auth_method = "jwt"
    remember_token = request.headers.get("X-Remember-Token")
    if remember_token:
        auth_method = "remember_token"
    new_jwt = getattr(request.state, 'new_jwt_token', None)
    return APIStandards.create_success_response(
        data={
            "user_id": str(user.id),
            "email": user.email,
            "auth_method": auth_method,
            "jwt_header_present": bool(auth_header),
            "remember_token_present": bool(remember_token),
            "new_jwt_generated": bool(new_jwt),
            "token_refresh_occurred": bool(new_jwt)
        },
        message="Authentication information retrieved successfully",
        title="Authentication Information"
    )

@router.post(
    "/auth-test/test-remember-token",
    summary="Test Remember Token Flow",
    description="Test the remember token authentication flow and JWT refresh",
    response_model=StandardResponse[dict]
)
async def test_remember_token_flow(
    request: Request,
    current_user: dict = Depends(get_current_user)
) -> JSONResponse:
    if isinstance(current_user, JSONResponse):
        return current_user
    user = current_user.get("user")
    if not user:
        return APIStandards.create_error_response(
            error_message="User information not available",
            error_title="Authentication Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
    remember_token = request.headers.get("X-Remember-Token")
    new_jwt = getattr(request.state, 'new_jwt_token', None)
    if remember_token and new_jwt:
        return APIStandards.create_success_response(
            data={
                "flow_type": "remember_token_to_jwt",
                "message": "Remember token was used to generate new JWT",
                "user_id": str(user.id),
                "email": user.email,
                "new_jwt_generated": True,
                "check_headers": "Look for X-New-JWT-Token in response headers"
            },
            message="Remember token flow test successful",
            title="Remember Token Flow Test"
        )
    elif remember_token:
        return APIStandards.create_success_response(
            data={
                "flow_type": "remember_token_only",
                "message": "Remember token was used but no new JWT was generated",
                "user_id": str(user.id),
                "email": user.email,
                "new_jwt_generated": False
            },
            message="Remember token authentication successful",
            title="Remember Token Flow Test"
        )
    else:
        return APIStandards.create_success_response(
            data={
                "flow_type": "jwt_only",
                "message": "JWT authentication was used",
                "user_id": str(user.id),
                "email": user.email,
                "new_jwt_generated": False
            },
            message="JWT authentication successful",
            title="JWT Authentication Test"
        )

# Debug endpoint to check remember token storage
@router.get(
    "/debug/check-remember-token",
    summary="Debug: Check Remember Token Storage",
    description="Debug endpoint to check if remember token is stored in database",
    response_model=StandardResponse[dict]
)
async def debug_check_remember_token(
    token: str,
    db: AsyncSession = Depends(get_db)
) -> JSONResponse:
    """Debug endpoint to check remember token storage"""
    try:
        from app.core.security.remember_token_manager import RememberTokenManager
        remember_token_manager = RememberTokenManager()
        
        # Check if token format is valid
        format_valid = remember_token_manager.validate_remember_token_format(token)
        
        # Try to authenticate with token
        user = await remember_token_manager.authenticate_with_remember_token(token, db)
        
        # Check database directly
        from sqlalchemy import select
        from app.models.user import User
        
        result = await db.execute(
            select(User).where(User.remember_me_token == token)
        )
        db_user = result.scalar_one_or_none()
        
        return APIStandards.create_success_response(
            data={
                "token_provided": token[:10] + "...",
                "format_valid": format_valid,
                "auth_successful": user is not None,
                "user_found_in_db": db_user is not None,
                "user_email": user.email if user else None,
                "db_user_email": db_user.email if db_user else None,
                "token_length": len(token),
                "token_pattern_match": bool(remember_token_manager.token_pattern.match(token))
            },
            message="Debug information retrieved",
            title="Remember Token Debug Info"
        )
        
    except Exception as e:
        logger.error(f"Error in debug endpoint: {e}", exc_info=True)
        return APIStandards.create_error_response(
            error_message=f"Debug error: {str(e)}",
            error_title="Debug Error",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_code=ErrorCodes.UNKNOWN_ERROR
        )
