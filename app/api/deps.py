"""
API Dependencies
Common dependencies for API endpoints
"""

from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import async_session

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get database session"""
    async with async_session() as session:
        try:
            yield session
        finally:
            await session.close() 