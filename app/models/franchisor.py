"""
Franchisor model for database operations
"""

from sqlalchemy import Column, String, Boolean, Float, DateTime
from sqlalchemy.sql import func
from app.models.base import Base
from sqlalchemy.dialects.postgresql import UUID
import uuid


class Franchisor(Base):
    """Franchisor model for storing franchisor information"""
    
    __tablename__ = "franchisors"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False, index=True)
    category = Column(String(100), nullable=False, index=True)
    region = Column(String(100), nullable=True, index=True)
    budget = Column(Float, nullable=True)
    sub_category = Column(String(100), nullable=True, index=True)
    brochure_url = Column(String(500), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    def __repr__(self):
        return f"<Franchisor(id={self.id}, name={self.name}, category={self.category})>" 