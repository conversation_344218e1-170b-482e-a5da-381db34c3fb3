"""Lead model"""
import uuid
from sqlalchemy import Column, String, DateTime, Text, Numeric, Integer, ForeignKey, Boolean, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.core.database.base import Base


class Lead(Base):
    __tablename__ = "leads"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    zoho_lead_id = Column(String(100), unique=True)
    full_name = Column(String(255), nullable=False)
    contact_number = Column(String(20), nullable=False, index=True)
    email = Column(String(255))
    location = Column(String(255))
    lead_source = Column(String(100))
    franchise_preference = Column(String(255))
    budget_preference = Column(Numeric(12, 2))
    qualification_status = Column(String(50), default="new")
    is_active = Column(Boolean, default=True, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    responses = relationship("LeadResponse", back_populates="lead")
    communications = relationship("Communication", back_populates="lead")
    
    def __repr__(self):
        return f"<Lead(id={self.id}, name={self.full_name}, status={self.qualification_status})>"


class Question(Base):
    __tablename__ = "questions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    question_text = Column(Text, nullable=False)
    question_type = Column(String(50))
    is_required = Column(Boolean, default=False)
    order_sequence = Column(Integer)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    responses = relationship("LeadResponse", back_populates="question")
    
    def __repr__(self):
        return f"<Question(id={self.id}, type={self.question_type}, required={self.is_required})>"


class LeadResponse(Base):
    __tablename__ = "lead_responses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"))
    question_id = Column(UUID(as_uuid=True), ForeignKey("questions.id"))
    response_text = Column(Text)
    answered_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    lead = relationship("Lead", back_populates="responses")
    question = relationship("Question", back_populates="responses")
    
    def __repr__(self):
        return f"<LeadResponse(id={self.id}, lead_id={self.lead_id}, question_id={self.question_id})>"


class Communication(Base):
    __tablename__ = "communications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    communication_type = Column(String(50), nullable=False)  # email, phone, note, meeting, etc.
    subject = Column(String(255))
    content = Column(Text)
    direction = Column(String(20))  # inbound, outbound, internal
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    # Relationships
    lead = relationship("Lead", back_populates="communications")

    def __repr__(self):
        return f"<Communication(id={self.id}, lead_id={self.lead_id}, type={self.communication_type})>"
