"""
Franchisor schemas for request and response models
"""

from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from enum import Enum

from app.schemas.base_response import (
    SuccessResponse,
    PaginationInfo
)


class FranchisorCategory(str, Enum):
    """Franchisor category enumeration"""
    FOOD_BEVERAGE = "food_beverage"
    RETAIL = "retail"
    HOME_SERVICES = "home_services"
    HEALTH_FITNESS = "health_fitness"
    EDUCATION = "education"
    AUTOMOTIVE = "automotive"
    BEAUTY_PERSONAL_CARE = "beauty_personal_care"
    TECHNOLOGY = "technology"
    REAL_ESTATE = "real_estate"
    OTHER = "other"


class FranchisorRegion(str, Enum):
    """Franchisor region enumeration"""
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"
    ASIA_PACIFIC = "asia_pacific"
    LATIN_AMERICA = "latin_america"
    MIDDLE_EAST = "middle_east"
    AFRICA = "africa"
    AUSTRALIA = "australia"
    GLOBAL = "global"


class FranchisorSubCategory(str, Enum):
    """Franchisor sub-category enumeration"""
    # Food & Beverage
    CAFE = "cafe"
    RESTAURANT = "restaurant"
    FAST_FOOD = "fast_food"
    BAKERY = "bakery"
    ICE_CREAM = "ice_cream"
    
    # Retail
    CLOTHING = "clothing"
    ELECTRONICS = "electronics"
    CONVENIENCE_STORE = "convenience_store"
    PHARMACY = "pharmacy"
    
    # Home Services
    CLEANING = "cleaning"
    LAWN_CARE = "lawn_care"
    PLUMBING = "plumbing"
    ELECTRICAL = "electrical"
    
    # Health & Fitness
    GYM = "gym"
    YOGA = "yoga"
    NUTRITION = "nutrition"
    MENTAL_HEALTH = "mental_health"
    
    # Education
    TUTORING = "tutoring"
    LANGUAGE_SCHOOL = "language_school"
    SKILLS_TRAINING = "skills_training"
    
    # Automotive
    CAR_WASH = "car_wash"
    AUTO_REPAIR = "auto_repair"
    TIRE_SERVICE = "tire_service"
    
    # Beauty & Personal Care
    SALON = "salon"
    SPA = "spa"
    NAIL_SALON = "nail_salon"
    
    # Technology
    IT_SERVICES = "it_services"
    SOFTWARE = "software"
    CONSULTING = "consulting"
    
    # Real Estate
    PROPERTY_MANAGEMENT = "property_management"
    REAL_ESTATE_AGENCY = "real_estate_agency"
    
    # Other
    OTHER = "other"


# Request Models
class FranchisorCreateRequest(BaseModel):
    """Request model for creating a franchisor"""
    name: str = Field(..., description="Franchisor name", min_length=1, max_length=255)
    category: FranchisorCategory = Field(..., description="Franchisor category")
    region: Optional[FranchisorRegion] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount", ge=0)
    sub_category: Optional[FranchisorSubCategory] = Field(None, description="Franchisor sub-category")
    is_active: bool = Field(True, description="Whether the franchisor is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Coffee Club Melbourne",
                "category": "food_beverage",
                "region": "australia",
                "budget": 250000.0,
                "sub_category": "cafe",
                "is_active": True
            }
        }
    )


class FranchisorUpdateRequest(BaseModel):
    """Request model for updating a franchisor"""
    name: Optional[str] = Field(None, description="Franchisor name", min_length=1, max_length=255)
    category: Optional[FranchisorCategory] = Field(None, description="Franchisor category")
    region: Optional[FranchisorRegion] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount", ge=0)
    sub_category: Optional[FranchisorSubCategory] = Field(None, description="Franchisor sub-category")
    is_active: Optional[bool] = Field(None, description="Whether the franchisor is active")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Coffee Club Melbourne Updated",
                "category": "food_beverage",
                "region": "australia",
                "budget": 300000.0,
                "sub_category": "cafe",
                "is_active": True
            }
        }
    )


class FranchisorImportRequest(BaseModel):
    """Request model for CSV import"""
    csv_file: str = Field(..., description="Base64 encoded CSV file content")
    
    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "csv_file": "bmFtZSxjYXRlZ29yeSxyZWdpb24sYnVkZ2V0LHN1Yl9jYXRlZ29yeSxpc19hY3RpdmUKQ29mZmVlIENsdWIsZm9vZF9iZXZlcmFnZSxhdXN0cmFsaWEsMjUwMDAwLGNhZmUsdHJ1ZQ=="
            }
        }
    )


# Response Models
class FranchisorResponse(BaseModel):
    """Response model for franchisor data"""
    id: str = Field(..., description="Unique franchisor identifier")
    name: str = Field(..., description="Franchisor name")
    category: FranchisorCategory = Field(..., description="Franchisor category")
    region: Optional[FranchisorRegion] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount")
    sub_category: Optional[FranchisorSubCategory] = Field(None, description="Franchisor sub-category")
    brochure_url: Optional[str] = Field(None, description="Brochure file URL")
    is_active: bool = Field(..., description="Whether the franchisor is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "id": "frc_123456789",
                "name": "Coffee Club Melbourne",
                "category": "food_beverage",
                "region": "australia",
                "budget": 250000.0,
                "sub_category": "cafe",
                "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                "is_active": True,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T12:00:00Z"
            }
        }
    )


class FranchisorListResponse(BaseModel):
    """Response model for franchisor list with pagination"""
    items: List[FranchisorResponse] = Field(..., description="List of franchisors")
    total_count: int = Field(..., description="Total number of franchisors")
    pagination: PaginationInfo = Field(..., description="Pagination information")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "items": [
                    {
                        "id": "frc_123456789",
                        "name": "Coffee Club Melbourne",
                        "category": "food_beverage",
                        "region": "australia",
                        "budget": 250000.0,
                        "sub_category": "cafe",
                        "brochure_url": "https://s3.amazonaws.com/bucket/brochures/coffee_club_brochure.pdf",
                        "is_active": True,
                        "created_at": "2024-01-01T00:00:00Z",
                        "updated_at": "2024-01-01T12:00:00Z"
                    }
                ],
                "total_count": 1,
                "pagination": {
                    "current_page": 1,
                    "total_pages": 1,
                    "items_per_page": 20,
                    "total_items": 1
                }
            }
        }
    )


class FranchisorImportResponse(BaseModel):
    """Response model for CSV import results"""
    total_rows: int = Field(..., description="Total number of rows processed")
    successful_imports: int = Field(..., description="Number of successfully imported rows")
    failed_imports: int = Field(..., description="Number of failed imports")
    errors: List[dict] = Field(..., description="List of import errors")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_rows": 10,
                "successful_imports": 8,
                "failed_imports": 2,
                "errors": [
                    {
                        "row": 3,
                        "field": "name",
                        "message": "Name is required"
                    },
                    {
                        "row": 7,
                        "field": "category",
                        "message": "Invalid category value"
                    }
                ]
            }
        }
    )


class FranchisorDeleteResponse(BaseModel):
    """Response model for franchisor deletion"""
    franchisor_id: str = Field(..., description="Deleted franchisor ID")
    deleted_at: datetime = Field(..., description="Deletion timestamp")
    message: str = Field(..., description="Deletion confirmation message")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "franchisor_id": "frc_123456789",
                "deleted_at": "2024-01-01T12:00:00Z",
                "message": "Franchisor has been permanently deleted"
            }
        }
    )


# Type aliases for response models
FranchisorSuccessResponse = SuccessResponse[FranchisorResponse]
FranchisorListSuccessResponse = SuccessResponse[FranchisorListResponse]
FranchisorImportSuccessResponse = SuccessResponse[FranchisorImportResponse]
FranchisorDeleteSuccessResponse = SuccessResponse[FranchisorDeleteResponse] 