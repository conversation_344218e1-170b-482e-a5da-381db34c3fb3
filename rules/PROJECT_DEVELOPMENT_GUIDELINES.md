# GrowthHive FastAPI Project - Complete Development Guidelines & Coding Conventions

## 🎯 Project Overview

This is a production-grade FastAPI application for franchise management with S3 integration, authentication, and comprehensive business logic. The project follows strict architectural patterns and coding standards developed through extensive real-world implementation.

**Current Project Status (Updated: 2025-06-25)**
- ✅ **Authentication System**: Fully implemented with JWT + refresh tokens + remember me functionality
- ✅ **User Management**: Complete CRUD operations with role-based access and profile management
- ✅ **Franchisor Management**: **FULLY INTEGRATED** with Category/Subcategory relationships + S3 integration + CSV import
- ✅ **Lead Management**: **FULLY ENHANCED** - Complete lead tracking with bulk CSV upload, advanced search, communication history, and duplicate detection
- ✅ **Category Module**: **PRODUCTION-READY** - Complete implementation with proper error handling
- ✅ **Subcategory Module**: **PRODUCTION-READY** - Full CRUD with category relationships
- ✅ **Category-Franchisor Integration**: **COMPLETE** - Foreign key relationships working perfectly
- ✅ **Database Models**: All models properly defined with UUID primary keys and relationships
- ✅ **API Standards**: Consistent response format across all endpoints with comprehensive error handling
- ✅ **Validation**: Category/subcategory relationship validation working correctly
- ✅ **CSV Import System**: Robust CSV import with validation and error reporting
- ✅ **S3 Integration**: Complete file upload system with validation and error handling
- ✅ **Middleware Stack**: Request logging, JWT refresh, CORS, and security headers
- ⚠️ **Testing**: Database connection issues in test environment need resolution
- ⚠️ **Search Functionality**: Basic search implemented, can be enhanced with full-text search

**Key Requirements & Principles:**
- All endpoints must be authenticated for access using JWT tokens
- Return proper error responses in all scenarios with consistent error codes
- Use UUID for all primary keys across the system for consistency
- Follow existing application conventions for request and response patterns
- Always use Alembic for database schema changes - never modify database directly
- Implement comprehensive validation at both Pydantic and service layers
- Use async/await patterns throughout for optimal performance

## 📋 Table of Contents

1. [Architecture & Structure](#architecture--structure)
2. [UUID Primary Key Strategy](#uuid-primary-key-strategy)
3. [Database Model Patterns](#database-model-patterns)
4. [Pydantic Schema Conventions](#pydantic-schema-conventions)
5. [Service Layer Architecture](#service-layer-architecture)
6. [API Endpoint Patterns](#api-endpoint-patterns)
7. [Response Structure Standards](#response-structure-standards)
8. [Error Handling & Exception Management](#error-handling--exception-management)
9. [Authentication & Security Implementation](#authentication--security-implementation)
10. [File Upload & S3 Integration](#file-upload--s3-integration)
11. [CSV Import System](#csv-import-system)
12. [Middleware Implementation](#middleware-implementation)
13. [Async/Await Patterns](#asyncawait-patterns)
14. [Database Migration Strategy](#database-migration-strategy)
15. [Testing Patterns & Fixtures](#testing-patterns--fixtures)
16. [Configuration Management](#configuration-management)
17. [Logging & Monitoring](#logging--monitoring)
18. [Performance Optimization](#performance-optimization)
19. [Code Quality & Linting](#code-quality--linting)
20. [Import & Dependency Management](#import--dependency-management)
21. [Category Module Analysis](#category-module-analysis)
22. [Franchisor Module Integration](#franchisor-module-integration)
23. [Lead Management System](#lead-management-system)
24. [Known Issues & Solutions](#known-issues--solutions)
25. [Development Workflow](#development-workflow)
26. [Production Deployment Guidelines](#production-deployment-guidelines)

---

## 🏗️ Architecture & Structure

### Project Structure (MUST MAINTAIN)

The GrowthHive project follows a clean, modular architecture that separates concerns effectively:

```
growthhive-cursor/
├── app/
│   ├── main.py                    # FastAPI application entry point with middleware setup
│   ├── api/                       # API layer - all HTTP endpoints
│   │   ├── deps.py               # Shared dependencies (database sessions, auth)
│   │   ├── middleware/           # Custom HTTP middlewares
│   │   └── v1/                   # API version 1 (versioned endpoints)
│   │       ├── api.py            # Router aggregation - includes all endpoint routers
│   │       ├── dependencies/     # Authentication and common dependencies
│   │       └── endpoints/        # Individual route handlers (users, auth, franchisors, etc.)
│   ├── core/                     # Core application functionality
│   │   ├── config/              # Settings management with Pydantic BaseSettings
│   │   ├── database/            # Database connection, session management
│   │   ├── security/            # Authentication, JWT handling, password hashing
│   │   ├── s3/                  # AWS S3 integration for file uploads
│   │   ├── utils/               # Helper utilities and common functions
│   │   ├── cache/               # Memory caching implementation
│   │   ├── logging/             # Structured logging setup
│   │   ├── exceptions/          # Custom exception classes
│   │   └── responses/           # Standard response models and error codes
│   ├── models/                   # SQLAlchemy database models
│   │   ├── __init__.py          # Model imports for Alembic
│   │   ├── user.py              # User and authentication models
│   │   ├── franchisor.py        # Franchisor business entity
│   │   ├── category.py          # Category taxonomy
│   │   ├── subcategory.py       # Subcategory with foreign key to category
│   │   ├── lead.py              # Lead management with communication history
│   │   └── ...                  # Other business models
│   ├── schemas/                  # Pydantic models for validation
│   │   ├── base_response.py     # Standard response wrappers
│   │   ├── user.py              # User request/response schemas
│   │   ├── franchisor.py        # Franchisor CRUD schemas
│   │   └── ...                  # Schema files mirror model structure
│   ├── services/                 # Business logic layer
│   │   ├── user_service.py      # User management business logic
│   │   ├── franchisor_service.py # Franchisor operations
│   │   ├── category_service.py  # Category management
│   │   └── ...                  # Service layer handles all business rules
│   ├── middleware/               # Global application middlewares
│   │   ├── request_logging_middleware.py # HTTP request/response logging
│   │   └── jwt_refresh_middleware.py     # Automatic JWT token refresh
│   └── utils/                    # Utility functions and helpers
├── tests/                        # Comprehensive test suite
│   ├── conftest.py              # Pytest fixtures and test configuration
│   ├── test_*.py                # Test files mirror app structure
│   └── fixtures/                # Test data and mock objects
├── alembic/                      # Database migration management
│   ├── env.py                   # Alembic configuration with model imports
│   ├── versions/                # Migration files (auto-generated)
│   └── alembic.ini             # Alembic settings
├── rules/                        # Project documentation and guidelines
├── requirements.txt              # Python dependencies
├── .env                          # Environment variables (not in git)
├── .cursorrules                  # Coding standards for Cursor AI
└── README.md                     # Project documentation
```

### Key Architectural Principles

#### 1. **Separation of Concerns**
- **Models**: Pure data structures with relationships
- **Schemas**: Input/output validation and serialization
- **Services**: Business logic and data processing
- **Endpoints**: HTTP request/response handling only
- **Core**: Infrastructure and cross-cutting concerns

#### 2. **Dependency Injection Pattern**
```python
# Consistent dependency injection throughout the application
async def create_user(
    user_data: UserCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user)
) -> UserResponse:
    user_service = UserService(db)
    return await user_service.create_user(user_data)
```

#### 3. **Async-First Architecture**
- All database operations are async
- All external API calls are async
- Proper session management with async context managers
- Connection pooling for optimal performance

#### 4. **Modular Router Structure**
```python
# Each module has its own router that gets included in main API router
from app.api.v1.endpoints import auth, users, franchisors, categories

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["User Management"])
api_router.include_router(franchisors.router, prefix="/franchisors", tags=["Franchisors"])
```

---

## 🔑 UUID Primary Key Strategy

### Universal UUID Implementation

**Critical Decision**: All primary keys in GrowthHive use UUID instead of auto-incrementing integers for several important reasons:

#### Why UUID Primary Keys?

1. **Global Uniqueness**: UUIDs are globally unique across all tables and databases
2. **Security**: No sequential ID enumeration attacks
3. **Distributed Systems**: Works well with microservices and database sharding
4. **API Consistency**: All endpoints use the same ID format
5. **Future-Proofing**: Easier to merge data from different sources

#### Implementation Pattern

```python
# Standard UUID primary key pattern used across all models
import uuid
from sqlalchemy import Column
from sqlalchemy.dialects.postgresql import UUID

class BaseModel(Base):
    __abstract__ = True

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
```

#### Specific Examples from Codebase

```python
# User model - uses UUID with proper typing
class User(Base):
    __tablename__ = "users"
    id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)

# Franchisor model - UUID with foreign key relationships
class Franchisor(Base):
    __tablename__ = "franchisors"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    category_id = Column(UUID(as_uuid=True), ForeignKey("category.id", ondelete="SET NULL"))

# Lead model - consistent UUID usage
class Lead(Base):
    __tablename__ = "leads"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
```

#### Foreign Key Relationships with UUIDs

```python
# Proper foreign key setup with UUIDs
class Subcategory(Base):
    __tablename__ = "subcategory"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("category.id", ondelete="CASCADE"),
        nullable=False,
        index=True
    )

    # Relationship definition
    category_rel = relationship("Category", foreign_keys=[category_id], lazy="select")
```

#### Pydantic Schema Integration

```python
# Schemas properly handle UUID serialization
class FranchisorResponse(BaseModel):
    id: str = Field(..., description="Unique franchisor identifier")  # UUID as string
    category_id: Optional[str] = Field(None, description="Category UUID")
    subcategory_id: Optional[str] = Field(None, description="Subcategory UUID")
```

#### Service Layer UUID Handling

```python
# Services generate custom UUID formats when needed
class FranchisorService:
    def _generate_franchisor_id(self) -> str:
        """Generate a custom franchisor ID with prefix"""
        base_uuid = str(uuid.uuid4())
        return f"frc_{base_uuid.replace('-', '')[:12]}"
```

### UUID Best Practices Observed

1. **Always Index UUID Primary Keys**: `index=True` for performance
2. **Use `as_uuid=True`**: Stores as native UUID type in PostgreSQL
3. **Default Generation**: `default=uuid.uuid4` for automatic generation
4. **String Serialization**: Convert to string for JSON responses
5. **Validation**: Use Pydantic for UUID format validation
6. **Custom Prefixes**: Add business-meaningful prefixes when needed

---

## �️ Database Model Patterns

### SQLAlchemy 2.0+ Implementation Standards

The GrowthHive project uses SQLAlchemy 2.0+ with modern patterns and best practices:

#### Base Model Pattern

```python
# All models inherit from a common base
from app.core.database.connection import Base

class BaseModelMixin:
    """Common fields for all models"""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
```

#### Standard Model Structure

```python
# Example: User model with modern SQLAlchemy patterns
from sqlalchemy import String, Boolean, DateTime, func
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from uuid import uuid4

class User(Base):
    """User model for GrowthHive"""
    __tablename__ = "users"

    # Primary key with UUID
    id: Mapped[UUID] = mapped_column(PGUUID(as_uuid=True), primary_key=True, default=uuid4)

    # Required fields with proper constraints
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False, index=True)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)

    # Optional fields with proper typing
    mobile: Mapped[Optional[str]] = mapped_column(String(20), unique=True, nullable=True)
    first_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    last_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Boolean fields with defaults
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False, index=True)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Timestamp fields with automatic management
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

#### Relationship Patterns

```python
# Foreign key relationships with proper cascade behavior
class Franchisor(Base):
    __tablename__ = "franchisors"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    name = Column(String(255), nullable=False, index=True)

    # Foreign key relationships with UUIDs
    category_id = Column(UUID(as_uuid=True), ForeignKey("category.id", ondelete="SET NULL"), nullable=True, index=True)
    subcategory_id = Column(UUID(as_uuid=True), ForeignKey("subcategory.id", ondelete="SET NULL"), nullable=True, index=True)

    # Relationship definitions with proper loading strategy
    category_rel = relationship("Category", foreign_keys=[category_id], lazy="select")
    subcategory_rel = relationship("Subcategory", foreign_keys=[subcategory_id], lazy="select")
```

#### Enum Integration

```python
# Proper enum usage in models
from enum import Enum

class FranchisorRegion(str, Enum):
    AUSTRALIA = "australia"
    NEW_ZEALAND = "new_zealand"
    ASIA_PACIFIC = "asia_pacific"
    NORTH_AMERICA = "north_america"
    EUROPE = "europe"

class Franchisor(Base):
    # Enum field with proper validation
    region = Column(String(100), nullable=True, index=True)  # Stored as string, validated in service layer
```

#### Index Strategy

```python
# Strategic indexing for performance
class Lead(Base):
    __tablename__ = "leads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)

    # Indexed fields for common queries
    contact_number = Column(String(20), nullable=False, index=True)  # Search by phone
    email = Column(String(255), index=True)  # Search by email
    qualification_status = Column(String(50), default="new", index=True)  # Filter by status

    # Composite indexes would be defined in migration files
```

#### Soft Delete Pattern

```python
# Soft delete implementation where needed
class Category(Base):
    __tablename__ = "category"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False, unique=True)
    is_active = Column(Boolean, default=True, nullable=False, index=True)
    is_deleted = Column(Boolean, default=False, nullable=False, index=True)  # Soft delete flag
```

#### Model Validation and Constraints

```python
# Database-level constraints and validation
class User(Base):
    __tablename__ = "users"

    # Unique constraints
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    mobile: Mapped[Optional[str]] = mapped_column(String(20), unique=True, nullable=True)

    # Check constraints (would be added in migrations)
    # ALTER TABLE users ADD CONSTRAINT check_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');
```

### Model Organization Best Practices

1. **One Model Per File**: Each model gets its own file in `app/models/`
2. **Import in `__init__.py`**: All models imported for Alembic discovery
3. **Consistent Naming**: Table names use lowercase with underscores
4. **Proper Relationships**: Use `relationship()` for ORM navigation
5. **Index Strategy**: Index foreign keys and commonly queried fields
6. **Timezone Awareness**: All datetime fields use `timezone=True`

---

## 📋 Pydantic Schema Conventions

### Schema Architecture Pattern

The GrowthHive project uses a comprehensive Pydantic schema system with clear separation of concerns:

#### Schema File Structure

```python
# Each domain has its own schema file mirroring the model structure
app/schemas/
├── base_response.py      # Common response wrappers
├── user.py              # User-related schemas
├── franchisor.py        # Franchisor CRUD schemas
├── category.py          # Category management schemas
└── auth.py              # Authentication schemas
```

#### Base Schema Patterns

```python
# Standard base schemas for CRUD operations
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional
from datetime import datetime

class FranchisorBase(BaseModel):
    """Base franchisor schema with common fields"""
    name: str = Field(..., description="Franchisor name", min_length=1, max_length=255)
    region: Optional[FranchisorRegion] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount", ge=0)
    is_active: bool = Field(True, description="Whether the franchisor is active")

class FranchisorCreateRequest(FranchisorBase):
    """Request schema for creating a franchisor"""
    # UUID-based relationships
    category_id: Optional[str] = Field(None, description="Category UUID from category table")
    subcategory_id: Optional[str] = Field(None, description="Subcategory UUID from subcategory table")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Coffee Club Melbourne",
                "category_id": "123e4567-e89b-12d3-a456-************",
                "subcategory_id": "123e4567-e89b-12d3-a456-************",
                "region": "australia",
                "budget": 250000.0,
                "is_active": True
            }
        }
    )

class FranchisorUpdateRequest(FranchisorBase):
    """Request schema for updating a franchisor"""
    name: Optional[str] = Field(None, description="Franchisor name", min_length=1, max_length=255)
    category_id: Optional[str] = Field(None, description="Category UUID")
    subcategory_id: Optional[str] = Field(None, description="Subcategory UUID")
    is_active: Optional[bool] = Field(None, description="Whether the franchisor is active")

class FranchisorResponse(BaseModel):
    """Response schema with full relationship details"""
    id: str = Field(..., description="Unique franchisor identifier")
    name: str = Field(..., description="Franchisor name")

    # Relationship fields
    category_id: Optional[str] = Field(None, description="Category UUID")
    subcategory_id: Optional[str] = Field(None, description="Subcategory UUID")
    category_details: Optional[CategoryResponse] = Field(None, description="Category details")
    subcategory_details: Optional[SubcategoryResponse] = Field(None, description="Subcategory details")

    # Additional fields
    region: Optional[str] = Field(None, description="Franchisor region")
    budget: Optional[float] = Field(None, description="Budget amount")
    brochure_url: Optional[str] = Field(None, description="S3 brochure URL")
    is_active: bool = Field(..., description="Whether the franchisor is active")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
```

#### Response Wrapper Patterns

```python
# Standardized response wrappers for consistency
from app.schemas.base_response import SuccessResponse, ListResponse

class FranchisorSuccessResponse(SuccessResponse[FranchisorResponse]):
    """Success response wrapper for single franchisor"""
    pass

class FranchisorListSuccessResponse(SuccessResponse[FranchisorListResponse]):
    """Success response wrapper for franchisor list"""
    pass

class FranchisorListResponse(ListResponse[FranchisorResponse]):
    """Paginated list response for franchisors"""
    pass
```

#### Validation Patterns

```python
# Custom validators using Pydantic v2
from pydantic import field_validator, model_validator

class UserCreateRequest(BaseModel):
    email: str = Field(..., description="User email address")
    password: str = Field(..., description="User password", min_length=8)
    first_name: Optional[str] = Field(None, max_length=100)

    @field_validator('email')
    @classmethod
    def validate_email(cls, v: str) -> str:
        """Validate email format"""
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', v):
            raise ValueError('Invalid email format')
        return v.lower()

    @field_validator('password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength"""
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one digit')
        return v
```

#### CSV Import Schema Pattern

```python
# Specialized schemas for CSV import operations
class FranchisorImportRequest(BaseModel):
    """Request model for CSV import"""
    csv_file: str = Field(..., description="Base64 encoded CSV file content")

class FranchisorImportResponse(BaseModel):
    """Response model for CSV import results"""
    total_rows: int = Field(..., description="Total number of rows processed")
    successful_imports: int = Field(..., description="Number of successfully imported rows")
    failed_imports: int = Field(..., description="Number of failed imports")
    errors: List[dict] = Field(..., description="List of import errors")
```

### Schema Best Practices Observed

1. **Inheritance Hierarchy**: Base → Create/Update → Response schemas
2. **Field Descriptions**: Every field has a clear description
3. **Validation Rules**: Use Field() with constraints (min_length, max_length, ge, le)
4. **Example Data**: Provide realistic examples in model_config
5. **Optional vs Required**: Clear distinction with proper typing
6. **UUID Handling**: UUIDs as strings in schemas, validated in services
7. **Relationship Data**: Include both IDs and full relationship details in responses
8. **Consistent Naming**: Schema names follow pattern: `{Entity}{Operation}Request/Response`

---

## �📊 Category Module Analysis

### ✅ Current Implementation Status

The **Category and Subcategory modules are FULLY FUNCTIONAL** with the following features:

#### **Category Module Features:**
- ✅ **CRUD Operations**: Create, Read, Update, Delete categories
- ✅ **Validation**: Duplicate name prevention with proper error handling
- ✅ **Search**: Basic search functionality implemented
- ✅ **Pagination**: Skip/limit pagination support
- ✅ **Authentication**: All endpoints properly secured
- ✅ **Error Handling**: Comprehensive error responses with proper HTTP status codes
- ✅ **Database Integration**: Full SQLAlchemy async support

#### **Subcategory Module Features:**
- ✅ **CRUD Operations**: Complete subcategory management
- ✅ **Category Relationship**: Proper foreign key relationships with cascade delete
- ✅ **Validation**: Duplicate name prevention within same category
- ✅ **Search & Filtering**: Search by name, filter by active status
- ✅ **Pagination**: Full pagination support
- ✅ **Authentication**: All endpoints secured with JWT
- ✅ **Error Handling**: Detailed error responses for all scenarios

#### **API Endpoints Available:**
```
Categories:
POST   /api/categories              - Create category
GET    /api/categories              - List categories (with search)
GET    /api/categories/{id}         - Get category by ID
PUT    /api/categories/{id}         - Update category

Subcategories:
POST   /api/categories/{id}/subcategories    - Create subcategory
GET    /api/categories/{id}/subcategories    - List subcategories by category
GET    /api/subcategories/{id}               - Get subcategory by ID
PUT    /api/subcategories/{id}               - Update subcategory
GET    /api/subcategories                    - Search all subcategories
```

#### **Database Schema:**
```sql
-- Categories table
CREATE TABLE category (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Subcategories table
CREATE TABLE subcategory (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(255),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    category_id INTEGER NOT NULL REFERENCES category(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### **Test Coverage:**
- ✅ **Category Tests**: 6 comprehensive test cases covering all scenarios
- ✅ **Subcategory Tests**: 6 comprehensive test cases covering all scenarios
- ⚠️ **Test Environment**: Database connection issues need resolution
2. [Coding Standards](#coding-standards)
3. [FastAPI Best Practices](#fastapi-best-practices)
4. [Database Guidelines](#database-guidelines)
5. [Authentication & Security](#authentication--security)
6. [S3 Integration Guidelines](#s3-integration-guidelines)
7. [Error Handling](#error-handling)
8. [Testing Standards](#testing-standards)
9. [Performance Guidelines](#performance-guidelines)
10. [Configuration Management](#configuration-management)
11. [Dependencies & Tech Stack](#dependencies--tech-stack)
12. [Development Workflow](#development-workflow)

---

## 🏗️ Architecture & Structure

### Project Structure (MUST MAINTAIN)

```
growthhive-cursor/
├── app/
│   ├── main.py                    # Entry point
│   ├── api/                       # API routes
│   │   ├── deps.py               # Shared dependencies
│   │   ├── middleware/           # Custom middlewares
│   │   └── v1/                   # API version 1
│   │       ├── api.py            # Router aggregation
│   │       ├── dependencies/     # Auth and common deps
│   │       └── endpoints/        # Route handlers
│   ├── core/                     # Core functionality
│   │   ├── config/              # Settings management
│   │   ├── database/            # DB connection & base
│   │   ├── security/            # Auth, JWT, password handling
│   │   ├── s3/                  # S3 integration
│   │   ├── utils/               # Helper utilities
│   │   └── responses/           # Response models
│   ├── models/                   # SQLAlchemy models
│   ├── schemas/                  # Pydantic models
│   ├── services/                 # Business logic
│   ├── middleware/               # Global middlewares
│   └── utils/                    # Utility functions
├── tests/                        # All tests
├── alembic/                      # Database migrations
├── requirements.txt              # Dependencies
├── .env                          # Environment variables
└── .cursorrules                  # Coding standards
```

### File Naming Conventions

- **Use lowercase with underscores**: `user_service.py`, `auth_router.py`
- **Models**: `user.py`, `franchise.py`, `lead.py`
- **Schemas**: `user.py`, `auth.py`, `franchise.py`
- **Services**: `user_service.py`, `franchise_service.py`
- **Endpoints**: `users.py`, `auth.py`, `franchises.py`

---

## 💻 Coding Standards

### Python/FastAPI Rules

#### ✅ DO:
- Use `async def` for async operations (DB, API calls)
- Use `def` for pure or sync logic
- Type all function signatures with proper hints
- Use Pydantic v2 for all input/output schemas
- Use descriptive variable names (`is_active`, `has_permission`)
- Use one-line syntax for simple conditions:
  ```python
  if user.is_admin: return user
  ```
- Use guard clauses (avoid `else` after `return`)
- Use `BaseModel` for all inputs/outputs (never raw dicts)

#### ❌ DON'T:
- Use deeply nested logic
- Have unused imports or variables
- Use raw dictionaries for inputs
- Use `else` after `return` statements
- Use unnecessary curly braces
- Duplicate code

### Function Design Patterns

#### RORO Pattern (Receive Object, Return Object)
```python
async def process_user_data(
    user_data: UserCreateRequest,
    db: AsyncSession
) -> UserResponse:
    """Process user data and return response"""
    # Implementation
    return UserResponse(...)
```

#### Guard Clauses
```python
async def get_user(user_id: str, db: AsyncSession) -> Optional[User]:
    if not user_id:
        return None
    
    if not is_valid_uuid(user_id):
        return None
    
    # Happy path
    result = await db.execute(select(User).where(User.id == user_id))
    return result.scalar_one_or_none()
```

---

## 🚀 FastAPI Best Practices

### Route Design

#### ✅ Standard Route Structure
```python
@router.post(
    "/users",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create User",
    description="Create a new user account"
)
async def create_user(
    user_data: UserCreateRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> UserResponse:
    """Create a new user"""
    return await user_service.create_user(user_data, db)
```

#### ✅ Dependency Injection
```python
# Use Depends() for shared logic
async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: AsyncSession = Depends(get_db)
) -> User:
    # Implementation
    pass
```

#### ✅ Response Models
- Always use `response_model` parameter
- Use Pydantic models for all responses
- Never return raw dictionaries

### Middleware Usage

#### ✅ Global Middleware
```python
# Add in main.py
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(JWTRefreshMiddleware)
app.add_middleware(CORSMiddleware, ...)
```

#### ✅ Lifespan Context Managers
```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    yield
    # Shutdown
```

---

## 🗄️ Database Guidelines

### SQLAlchemy 2.0+ Patterns

#### ✅ Model Definition
```python
class User(Base):
    __tablename__ = "users"
    
    id: Mapped[UUID] = mapped_column(primary_key=True, default=uuid4)
    email: Mapped[str] = mapped_column(unique=True, index=True)
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
```

#### ✅ Async Database Operations
```python
# Query with select()
result = await db.execute(
    select(User).where(User.email == email)
)
user = result.scalar_one_or_none()

# Insert
db.add(new_user)
await db.commit()
await db.refresh(new_user)

# Update
await db.execute(
    update(User)
    .where(User.id == user_id)
    .values(is_active=False)
)
await db.commit()
```

#### ✅ Database Sessions
```python
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with async_session_maker() as session:
        try:
            yield session
        finally:
            await session.close()
```

---

## 🔐 Authentication & Security

### JWT Implementation

#### ✅ Token Structure
```python
class TokenData(BaseModel):
    email: Optional[str] = None
    user_id: Optional[str] = None
    exp: Optional[datetime] = None
```

#### ✅ Authentication Flow
1. User login → JWT access token + refresh token
2. Access token for API calls (short-lived)
3. Refresh token for new access tokens (long-lived)
4. Remember me token for persistent sessions

#### ✅ Security Middleware
```python
# JWT Refresh Middleware
class JWTRefreshMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Implementation
        pass
```

### Password Security

#### ✅ Password Hashing
```python
from passlib.context import CryptContext

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)
```

---

## ☁️ S3 Integration Guidelines

### Configuration Requirements

#### ✅ Environment Variables (REQUIRED)
```bash
# AWS Credentials
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_DEFAULT_REGION=ap-south-1

# S3 Configuration
S3_BUCKET=your_bucket_name
S3_BASE_URL=https://your-bucket.s3.region.amazonaws.com/

# Optional Settings
S3_UPLOAD_FOLDER=growthhive/
S3_BROCHURE_FOLDER=brochure/
S3_MAX_FILE_SIZE=10485760  # 10MB
S3_ALLOWED_EXTENSIONS=pdf,doc,docx,jpg,jpeg,png,gif
```

#### ✅ S3 Service Structure
```python
class S3Service:
    def __init__(self, s3_client: S3Client):
        self.client = s3_client
    
    async def upload_brochure(
        self,
        file: UploadFile,
        franchisor_name: str,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        # Implementation
        pass
```

#### ✅ File Validation
```python
def validate_file(
    file: UploadFile,
    max_size: int = 10 * 1024 * 1024,
    allowed_extensions: List[str] = ["pdf", "doc", "docx"]
) -> bool:
    # Implementation
    pass
```

---

## ⚠️ Error Handling

### Exception Hierarchy

#### ✅ Custom Exceptions
```python
class GrowthHiveException(Exception):
    def __init__(self, error_key: str, message: Dict, status_code: int, error_code: int, details: Optional[Dict] = None):
        self.error_key = error_key
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details
        super().__init__(message.get("description", "An error occurred"))

class AuthenticationError(GrowthHiveException):
    pass

class ValidationError(GrowthHiveException):
    pass

class DatabaseError(GrowthHiveException):
    pass
```

#### ✅ Exception Handler
```python
@app.exception_handler(GrowthHiveException)
async def growthhive_exception_handler(request: Request, exc: GrowthHiveException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.message,
            "error_code": exc.error_code,
            "data": exc.details or {}
        }
    )
```

#### ✅ Error Response Pattern
```python
def error_response(
    error_code: int,
    title: str,
    description: str,
    details: Optional[Dict] = None
) -> StandardResponse:
    return StandardResponse(
        success=False,
        message=MessageResponse(title=title, description=description),
        error_code=error_code,
        data=details or {}
    )
```

---

## 🧪 Testing Standards

### Test Structure

#### ✅ Test File Organization
```
tests/
├── conftest.py              # Shared fixtures
├── test_auth_endpoints.py   # Authentication tests
├── test_users.py           # User management tests
├── test_franchises.py      # Franchise tests
└── test_s3_integration.py  # S3 tests
```

#### ✅ Test Patterns
```python
@pytest.mark.asyncio
async def test_create_user_success(client: AsyncClient, db_session):
    """Test successful user creation"""
    response = await client.post(
        "/api/v1/users",
        json={
            "email": "<EMAIL>",
            "password": "Test123!@#",
            "first_name": "John",
            "last_name": "Doe"
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["data"]["email"] == "<EMAIL>"
```

#### ✅ Fixtures
```python
@pytest.fixture
async def test_user(db_session):
    user = User(
        email="<EMAIL>",
        password_hash=password_hasher.hash_password("TestPassword123!"),
        is_active=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user
```

---

## ⚡ Performance Guidelines

### Async Operations

#### ✅ Database Operations
- All DB operations must be async
- Use connection pooling
- Implement proper session management

#### ✅ External API Calls
```python
async def call_external_api(url: str, data: Dict) -> Dict:
    async with httpx.AsyncClient() as client:
        response = await client.post(url, json=data)
        return response.json()
```

#### ✅ Caching Strategy
```python
# Use Redis or in-memory caching for repeated calls
from app.core.cache.memory import MemoryCache

cache = MemoryCache()

async def get_user_with_cache(user_id: str) -> Optional[User]:
    cache_key = f"user:{user_id}"
    
    # Try cache first
    cached_user = cache.get(cache_key)
    if cached_user:
        return cached_user
    
    # Fetch from DB
    user = await get_user_from_db(user_id)
    if user:
        cache.set(cache_key, user, ttl=300)  # 5 minutes
    
    return user
```

### Optimization Rules

#### ✅ Lazy Loading
- Load relationships only when needed
- Use `selectinload()` for eager loading when required

#### ✅ Pagination
```python
async def get_users_paginated(
    db: AsyncSession,
    skip: int = 0,
    limit: int = 100
) -> List[User]:
    result = await db.execute(
        select(User)
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()
```

---

## ⚙️ Configuration Management

### Settings Structure

#### ✅ Pydantic Settings
```python
class Settings(BaseSettings):
    # Database
    DATABASE_URL: str
    
    # JWT
    SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # S3
    AWS_ACCESS_KEY_ID: str
    AWS_SECRET_ACCESS_KEY: str
    S3_BUCKET: str
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"

settings = Settings()
```

#### ✅ Environment Variables Priority
1. Environment variables
2. `.env` file
3. Default values

---

## 📦 Dependencies & Tech Stack

### Required Dependencies

#### ✅ Core Dependencies
```txt
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
sqlalchemy>=2.0.0
alembic>=1.12.0
python-multipart>=0.0.6
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
httpx>=0.25.0
boto3>=1.34.0
botocore>=1.34.0
```

#### ✅ Development Dependencies
```txt
pytest>=7.4.0
pytest-asyncio>=0.21.0
ruff>=0.1.0
mypy>=1.7.0
```

### Technology Stack

#### ✅ Backend
- **Framework**: FastAPI
- **Database**: PostgreSQL with SQLAlchemy 2.0+
- **Authentication**: JWT with refresh tokens
- **File Storage**: AWS S3
- **Migrations**: Alembic
- **Testing**: Pytest with async support

#### ✅ Development Tools
- **Linting**: Ruff
- **Type Checking**: MyPy
- **Code Formatting**: Black (via Ruff)
- **Import Sorting**: isort (via Ruff)

---

## 🔄 Development Workflow

### Code Quality Checks

#### ✅ Pre-commit Checklist
1. Run linting: `ruff check app/`
2. Run type checking: `mypy app/`
3. Run tests: `pytest`
4. Ensure all imports are used
5. Verify no syntax errors

#### ✅ Code Review Standards
- All functions must have type hints
- All functions must have docstrings
- No unused imports or variables
- Follow naming conventions
- Use proper error handling

### Git Workflow

#### ✅ Commit Messages
```
feat: add user authentication endpoint
fix: resolve database connection issue
docs: update API documentation
refactor: improve error handling in S3 service
test: add unit tests for user service
```

#### ✅ Branch Naming
- `feature/user-authentication`
- `fix/s3-upload-issue`
- `refactor/error-handling`
- `docs/api-documentation`

---

## 🚨 Critical Rules (MUST FOLLOW)

### 1. **Never Remove Existing Functionality**
- Only add new features or fix bugs
- Don't refactor working code without explicit instructions

### 2. **Maintain Project Structure**
- Follow the exact folder structure
- Use the specified naming conventions
- Keep files in their designated locations

### 3. **Async-First Development**
- All database operations must be async
- All external API calls must be async
- Use proper async/await patterns

### 4. **Type Safety**
- All functions must have type hints
- Use Pydantic models for all data validation
- Never use `Any` unless absolutely necessary

### 5. **Error Handling**
- Always handle exceptions properly
- Use custom exception classes
- Provide meaningful error messages

### 6. **Security First**
- Validate all inputs
- Use proper authentication
- Sanitize user data
- Follow OWASP guidelines

### 7. **Performance Considerations**
- Use connection pooling
- Implement caching where appropriate
- Optimize database queries
- Use lazy loading for relationships

### 8. **Testing Requirements**
- Write tests for new features
- Maintain test coverage
- Use proper test fixtures
- Test both success and failure cases

---

## 📚 Additional Resources

### Documentation
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [SQLAlchemy 2.0 Documentation](https://docs.sqlalchemy.org/en/20/)
- [Pydantic Documentation](https://docs.pydantic.dev/)
- [Alembic Documentation](https://alembic.sqlalchemy.org/)

### Best Practices
- [FastAPI Best Practices](https://fastapi.tiangolo.com/tutorial/best-practices/)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- [Async Python](https://docs.python.org/3/library/asyncio.html)

---

## 🎯 Success Criteria

A successful implementation should:
1. ✅ Follow all coding standards
2. ✅ Pass all linting checks
3. ✅ Pass all type checks
4. ✅ Pass all tests
5. ✅ Maintain existing functionality
6. ✅ Follow security best practices
7. ✅ Be performant and scalable
8. ✅ Be well-documented
9. ✅ Handle errors gracefully
10. ✅ Be production-ready

---

**Remember**: This project is production-grade and follows enterprise-level standards. Always prioritize stability, security, and maintainability over quick fixes or shortcuts.



```
from enum import Enum
from typing import Any, List, Optional, Dict, Union
from pydantic import BaseModel, Field

# ========================
# ENUMS (UPPERCASE FORMAT)
# ========================
class UserType(str, Enum):
    ADMIN = "ADMIN"
    NORMAL = "NORMAL"
    GUEST = "GUEST"
    ULTIMATE = "ULTIMATE"
    DIVINE = "DIVINE"

# ========================
# MESSAGE STRUCTURE
# ========================
class Message(BaseModel):
    title: Optional[str] = Field(default="", example="Data Retrieved Successfully")
    description: str = Field(..., example="Fetched data successfully.")

# ========================
# PAGINATION DETAILS
# ========================
class Pagination(BaseModel):
    current_page: int
    total_pages: int
    items_per_page: int
    total_items: int

# ========================
# BASE RESPONSE STRUCTURE
# ========================
class ApiResponse(BaseModel):
    success: bool = Field(..., example=True)
    message: Message
    data: Union[Dict[str, Any], List[Any], str, int, float, bool] = Field(default_factory=dict)
    error_code: Optional[int] = Field(default=None, example=1000)

# ========================
# PAGINATED RESPONSE WRAPPER (IF NEEDED)
# ========================
class PaginatedData(BaseModel):
    details: List[Dict[str, Any]]
    pagination: Pagination

# ========================
# FACTORY FUNCTIONS
# ========================
def success_response(
    details: Union[dict, list, str, int, float, bool],
    title: str = "Success",
    description: str = "Request completed successfully"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={"details": details}
    )

def paginated_response(
    details: list,
    current_page: int,
    total_pages: int,
    items_per_page: int,
    total_items: int,
    title: str = "Success",
    description: str = "Paginated data fetched"
) -> ApiResponse:
    return ApiResponse(
        success=True,
        message=Message(title=title, description=description),
        data={
            "details": details,
            "pagination": Pagination(
                current_page=current_page,
                total_pages=total_pages,
                items_per_page=items_per_page,
                total_items=total_items
            )
        }
    )

def error_response(
    error_code: int,
    title: str,
    description: str,
    http_success: bool = False
) -> ApiResponse:
    return ApiResponse(
        success=http_success,
        message=Message(title=title, description=description),
        data={},
        error_code=error_code
    )

```

---

## 🎯 Lead Management API Patterns & Best Practices

### Overview

The Lead Management API v2 implementation demonstrates advanced patterns for bulk operations, communication history, and enhanced search functionality. This section documents the key patterns and learnings from implementing a comprehensive lead management system.

### Key Features Implemented

1. **Enhanced Search & Filtering**
   - Full-text search across multiple fields (name, email, contact_number, location, franchise_preference)
   - Date range filtering (created_from, created_to)
   - Lead source filtering
   - Sorting options (name_asc, name_desc, created_asc, created_desc)

2. **Bulk CSV Upload with Duplicate Detection**
   - Streaming CSV processing with `csv.DictReader`
   - Duplicate detection on contact_number and email
   - Comprehensive error reporting with row-level details
   - Bulk database operations using `session.bulk_save_objects()`

3. **Communication History Tracking**
   - Separate Communication model for lead interaction history
   - Support for multiple communication types (email, phone, note, meeting)
   - Direction tracking (inbound, outbound, internal)
   - User attribution for audit trails

### Service Layer Patterns

#### Enhanced Search Implementation

```python
async def get_leads(
    self,
    skip: int = 0,
    limit: int = 20,
    status: Optional[str] = None,
    search: Optional[str] = None,
    lead_source: Optional[str] = None,
    created_from: Optional[datetime] = None,
    created_to: Optional[datetime] = None,
    sort: Optional[str] = None,
    user_role: str = None,
    user_id: str = None
) -> Tuple[List[LeadResponse], int]:
    """Enhanced search with multiple filters and sorting"""

    # Build dynamic query with filters
    filters = []
    if status:
        filters.append(Lead.qualification_status == status)
    if lead_source:
        filters.append(Lead.lead_source.ilike(f"%{lead_source}%"))
    if created_from:
        filters.append(Lead.created_at >= created_from)
    if created_to:
        filters.append(Lead.created_at <= created_to)
    if search:
        # Full-text search across multiple fields
        search_filter = or_(
            Lead.full_name.ilike(f"%{search}%"),
            Lead.email.ilike(f"%{search}%"),
            Lead.contact_number.ilike(f"%{search}%"),
            Lead.location.ilike(f"%{search}%"),
            Lead.franchise_preference.ilike(f"%{search}%")
        )
        filters.append(search_filter)

    # Apply dynamic sorting
    if sort == "name_asc":
        query = query.order_by(Lead.full_name.asc())
    elif sort == "name_desc":
        query = query.order_by(Lead.full_name.desc())
    # ... other sort options
```

#### Bulk Upload with Duplicate Detection

```python
async def bulk_upload_leads(self, file: UploadFile, created_by: str = None) -> Dict[str, Any]:
    """Bulk upload with comprehensive validation and duplicate detection"""

    # Validate file type
    if not file.filename or not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="File must be a CSV file")

    # Parse CSV with streaming
    content = await file.read()
    csv_content = content.decode('utf-8')
    csv_reader = csv.DictReader(io.StringIO(csv_content))

    # Validate headers
    required_headers = {
        'full_name', 'contact_number', 'email', 'location',
        'lead_source', 'franchise_preference', 'budget_preference',
        'qualification_status'
    }
    if not required_headers.issubset(set(csv_reader.fieldnames or [])):
        missing_headers = required_headers - set(csv_reader.fieldnames or [])
        raise HTTPException(
            status_code=422,
            detail=f"Missing required headers: {', '.join(missing_headers)}"
        )

    # Pre-fetch existing data for duplicate detection
    existing_contacts_query = select(Lead.contact_number).where(Lead.contact_number.isnot(None))
    existing_emails_query = select(Lead.email).where(Lead.email.isnot(None))

    existing_contacts_result = await self.db.execute(existing_contacts_query)
    existing_emails_result = await self.db.execute(existing_emails_query)

    existing_contacts = {row[0] for row in existing_contacts_result.fetchall()}
    existing_emails = {row[0] for row in existing_emails_result.fetchall()}

    # Process rows with validation and duplicate detection
    leads_to_create = []
    duplicates = []
    errors = []
    processed_contacts = set()
    processed_emails = set()

    for row_number, row in enumerate(csv_reader, start=2):
        try:
            # Trim whitespace
            row = {k: v.strip() if v else None for k, v in row.items()}

            # Validate required fields
            if not row.get('full_name') or not row.get('contact_number'):
                errors.append(f"Row {row_number}: Missing required fields")
                continue

            # Check duplicates
            contact_number = row['contact_number']
            email = row.get('email')

            if contact_number in processed_contacts or contact_number in existing_contacts:
                duplicates.append(f"Row {row_number}: Duplicate contact number {contact_number}")
                continue

            if email and (email in processed_emails or email in existing_emails):
                duplicates.append(f"Row {row_number}: Duplicate email {email}")
                continue

            # Convert and validate data types
            budget_preference = None
            if row.get('budget_preference'):
                try:
                    budget_preference = Decimal(str(row['budget_preference']))
                except (InvalidOperation, ValueError):
                    errors.append(f"Row {row_number}: Invalid budget_preference value")
                    continue

            # Create lead object
            lead = Lead(
                id=uuid.uuid4(),
                full_name=row['full_name'],
                contact_number=contact_number,
                email=email,
                location=row.get('location'),
                lead_source=row.get('lead_source'),
                franchise_preference=row.get('franchise_preference'),
                budget_preference=budget_preference,
                qualification_status=row.get('qualification_status', 'new')
            )

            leads_to_create.append(lead)
            processed_contacts.add(contact_number)
            if email:
                processed_emails.add(email)

        except Exception as e:
            errors.append(f"Row {row_number}: {str(e)}")
            continue

    # Bulk save with transaction
    if leads_to_create:
        self.db.add_all(leads_to_create)
        await self.db.commit()

        # Refresh for timestamps
        for lead in leads_to_create:
            await self.db.refresh(lead)

    return {
        "total_processed": row_number - 1,
        "successful_imports": len(leads_to_create),
        "duplicates_found": len(duplicates),
        "errors_found": len(errors),
        "duplicates": duplicates,
        "errors": errors
    }
```

### API Endpoint Patterns

#### Enhanced List Endpoint with Multiple Filters

```python
@router.get("/", response_model=LeadListSuccessResponse)
async def list_leads(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    status: Optional[str] = Query(None, description="Filter by qualification status"),
    search: Optional[str] = Query(None, description="Search leads by name, email, or contact number"),
    lead_source: Optional[str] = Query(None, description="Filter by lead source"),
    created_from: Optional[datetime] = Query(None, description="Filter leads created from this date"),
    created_to: Optional[datetime] = Query(None, description="Filter leads created until this date"),
    sort: Optional[str] = Query(None, description="Sort order: name_asc, name_desc, created_asc, created_desc"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Enhanced list endpoint with comprehensive filtering and sorting"""
    lead_service = LeadService(db)
    leads, total_count = await lead_service.get_leads(
        skip=skip,
        limit=limit,
        status=status,
        search=search,
        lead_source=lead_source,
        created_from=created_from,
        created_to=created_to,
        sort=sort,
        user_role=current_user.get("role"),
        user_id=current_user.get("user_id")
    )
    # ... response handling
```

#### Bulk Upload Endpoint with File Handling

```python
@router.post("/bulk-upload", response_model=BulkUploadSuccessResponse)
async def bulk_upload_leads(
    file: UploadFile = File(..., description="CSV file containing lead data"),
    current_user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Bulk upload leads from CSV file.

    Expected CSV headers:
    - full_name (required)
    - contact_number (required)
    - email, location, lead_source, franchise_preference, budget_preference, qualification_status

    The endpoint performs duplicate detection based on contact_number and email.
    """
    lead_service = LeadService(db)
    result = await lead_service.bulk_upload_leads(
        file=file,
        created_by=current_user.get("user_id")
    )

    return BulkUploadSuccessResponse(
        success=True,
        status="success",
        message=ResponseMessage(
            title="Bulk Upload Completed",
            description=f"Processed {result['total_processed']} rows, imported {result['successful_imports']} leads"
        ),
        data=result
    )
```

### Key Learnings & Best Practices

#### 1. Bulk Operations Performance
- **Use streaming for large files**: `csv.DictReader` with `io.StringIO` for memory efficiency
- **Pre-fetch existing data**: Query existing records once for duplicate detection
- **Batch database operations**: Use `session.add_all()` for bulk inserts
- **Transaction management**: Wrap bulk operations in transactions with proper rollback

#### 2. Duplicate Detection Strategy
- **Multi-field uniqueness**: Check both contact_number and email for duplicates
- **In-memory tracking**: Use sets to track processed values within the same file
- **Database validation**: Query existing records to prevent database-level duplicates
- **Detailed reporting**: Provide row-level duplicate and error information

#### 3. Enhanced Search Implementation
- **Dynamic query building**: Build SQLAlchemy queries dynamically based on provided filters
- **Full-text search**: Use `ilike` with wildcards for case-insensitive search across multiple fields
- **Date range filtering**: Support created_from and created_to for temporal filtering
- **Flexible sorting**: Support multiple sort options with clear naming conventions

#### 4. Communication History Design
- **Separate model**: Use dedicated Communication model for audit trail
- **Flexible types**: Support multiple communication types (email, phone, note, meeting)
- **Direction tracking**: Track inbound/outbound/internal communications
- **User attribution**: Link communications to users for accountability

#### 5. Error Handling Patterns
- **Comprehensive validation**: Validate at multiple levels (file type, headers, data types)
- **Graceful degradation**: Continue processing valid rows even when some rows fail
- **Detailed error reporting**: Provide specific error messages with row numbers
- **HTTP status codes**: Use appropriate status codes (400 for file issues, 422 for validation)

#### 6. Testing Considerations
- **Mock file uploads**: Use `io.BytesIO` and `UploadFile` for testing CSV uploads
- **Database fixtures**: Create proper test data with relationships
- **Edge case testing**: Test empty files, invalid headers, duplicate data
- **Transaction isolation**: Ensure tests don't interfere with each other

### Migration Strategy

When implementing similar functionality:

1. **Start with models**: Define database models with proper relationships
2. **Create migrations**: Use Alembic for schema changes
3. **Build service layer**: Implement business logic with comprehensive validation
4. **Add API endpoints**: Create RESTful endpoints with proper documentation
5. **Write tests**: Create comprehensive test suite with edge cases
6. **Document patterns**: Update guidelines with new patterns and learnings

This Lead Management API implementation serves as a reference for building robust, scalable APIs with advanced features like bulk operations, enhanced search, and audit trails.